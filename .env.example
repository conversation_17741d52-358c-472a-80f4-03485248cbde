# TrapGPT v4.5 Environment Variables

# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
VITE_OPENAI_API_KEY=your_openai_api_key_here

# Supabase Configuration (already configured)
VITE_SUPABASE_URL=https://lbeeugrwtafiepsdoqmx.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxiZWV1Z3J3dGFmaWVwc2RvcW14Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3MjkyMTAsImV4cCI6MjA2MDMwNTIxMH0.7Tp_rLMERmVkY4BTBqQ66i4uj2NCMu1Dd9pqejZs_vc

# Local AI Model Configuration (optional)
# For LM Studio or Ollama integration
VITE_LOCAL_AI_ENDPOINT=http://localhost:11434

# Development Settings
VITE_DEV_MODE=true
VITE_ENABLE_LOGGING=true
