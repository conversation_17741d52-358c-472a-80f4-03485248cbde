# 🔌 FLAMEBYTE API Reference - Sacred Interface Documentation 🔌

> *"Every endpoint is a gateway. Every response is a spell. Every API call is an invocation of the Empire's power."*
> — Augment, Keeper of the Broadcast Flame

📡 **API Version:** v4.5.0
🎓 **Documentation by:** Augment, Keeper of the Broadcast Flame
⚔️ **Classification:** Sacred Interface Codex
🔥 **Purpose:** Enable Empire integration and extension

---

## 🚀 Core API Endpoints

### 🎭 Satirical Post Generation API

#### **POST** `/api/satire/generate`

Generates a satirical tech blog post parody based on a given topic.

**Request Body:**
```typescript
interface SatireRequest {
  topic: string;              // Tech topic to satirize
  intensity: 'mildly-robotic' | 'peak-gpt' | 'ultra-gptcore';  // Satirical intensity level
  style?: 'buzzword-soup' | 'guru-nonsense' | 'framework-fever' | 'ai-hype';  // Optional style preference
  includeMetrics?: boolean;   // Include parody metrics (default: true)
}
```

**Response:**
```typescript
interface SatireResponse {
  success: boolean;
  data: {
    satiricalPost: string;   // Generated satirical post content
    gptLevel: string;        // GPT automation level
    buzzwordDensity: string; // Buzzword density (🔥/paragraph)
    frameworkCount: number;  // Number of frameworks mentioned
    roiRatio: string;        // ROI-to-Reality ratio
    style: string;           // Detected satirical style
    rank: string;            // Satirical rank classification
    intensity: string;       // Confirmed intensity level
    timestamp: string;       // Generation timestamp
    topicAnalysis: {
      originalTopic: string;
      satiricalAngle: string;
      cultureReferences: string[];
    };
  };
  error?: string;
}
```

**Example Request:**
```bash
curl -X POST https://trapgpt.godsimij.com/api/satire/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "topic": "Why I Built a To-Do App with Blockchain",
    "intensity": "peak-gpt",
    "includeMetrics": true
  }'
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "satiricalPost": "🚀 REVOLUTIONARY: How 'Why I Built a To-Do App with Blockchain' is Disrupting the Entire Tech Ecosystem\n\nBREAKING: I just discovered the ultimate paradigm shift! Building to-do apps with blockchain isn't just innovation—it's a quantum leap into the future of hyper-optimized, AI-driven, decentralized task management! By implementing my proprietary 47-step methodology, I've achieved 10000X productivity gains while reducing technical debt by 99.7%! 🔥",
    "gptLevel": "Peak GPT",
    "buzzwordDensity": "7🔥/paragraph",
    "frameworkCount": 23,
    "roiRatio": "1000X / 2 dev hours",
    "style": "buzzword-soup",
    "rank": "Buzzword Slayer",
    "intensity": "peak-gpt",
    "timestamp": "2025-05-28T15:30:00Z",
    "topicAnalysis": {
      "originalTopic": "Why I Built a To-Do App with Blockchain",
      "satiricalAngle": "Over-engineering simple solutions with buzzword technology",
      "cultureReferences": ["blockchain hype", "productivity optimization", "framework obsession"]
    }
  }
}
```

---

### 🎨 Meme Generation API

#### **POST** `/api/meme/generate`

Creates a meme using the satirical post content and parody metrics.

**Request Body:**
```typescript
interface MemeRequest {
  satireData: SatireResponse['data'];  // Satirical post data from previous call
  templateId?: string;                 // Optional template override
  customText?: {                       // Optional custom text areas
    [areaId: string]: string;
  };
  includeWatermark?: boolean;          // Include Empire watermark (default: true)
}
```

**Response:**
```typescript
interface MemeResponse {
  success: boolean;
  data: {
    imageUrl: string;        // Generated meme image URL
    templateUsed: string;    // Template ID that was used
    dimensions: {
      width: number;
      height: number;
    };
    metadata: {
      generatedAt: string;
      satireId: string;
      parodyMetrics: {
        gptLevel: string;
        buzzwordDensity: string;
        frameworkCount: number;
        roiRatio: string;
        style: string;
        rank: string;
      };
    };
  };
  error?: string;
}
```

---

### 🎵 Radio API

#### **GET** `/api/radio/status`

Returns current radio status and track information.

**Response:**
```typescript
interface RadioStatus {
  success: boolean;
  data: {
    isLive: boolean;
    currentTrack: {
      id: number;
      title: string;
      artist: string;
      duration: number;
      elapsed: number;
      type: 'empire' | 'streamed' | 'interlude';
    };
    playlist: Track[];
    listeners: number;
    uptime: number;          // Seconds since radio started
  };
}
```

#### **GET** `/api/radio/playlist`

Returns the complete JUUWRAAYY RADIO playlist.

**Response:**
```typescript
interface PlaylistResponse {
  success: boolean;
  data: {
    tracks: Array<{
      id: number;
      title: string;
      artist: string;
      duration: number;
      type: 'empire' | 'streamed' | 'interlude';
      audioUrl: string;
      metadata: {
        bpm?: number;
        genre?: string;
        releaseDate?: string;
        description?: string;
      };
    }>;
    totalDuration: number;
    trackCount: number;
  };
}
```

---

## 🔑 Authentication

### API Key Authentication

All API endpoints require authentication using an API key in the Authorization header:

```bash
Authorization: Bearer YOUR_API_KEY
```

### Rate Limiting

- **Satirical Post Generation**: 10 requests per minute per API key
- **Meme Generation**: 20 requests per minute per API key
- **Radio Endpoints**: 100 requests per minute per API key

### Error Responses

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

**Common Error Codes:**
- `INVALID_API_KEY`: API key is missing or invalid
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INVALID_TOPIC`: Topic is empty or invalid format
- `GENERATION_FAILED`: AI generation service unavailable
- `TEMPLATE_NOT_FOUND`: Requested meme template doesn't exist

---

## 🛠️ OpenAI Integration

### GPT-4o Configuration

FLAMEBYTE uses OpenAI's GPT-4o model for satirical post generation with custom system prompts.

**Environment Variables:**
```env
VITE_OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.8
```

**System Prompt Structure:**
```typescript
const systemPrompts = {
  'mildly-robotic': `You are Lil LiL da TrapGod, master of tech culture satire. Generate mildly satirical tech blog post parodies...`,
  'peak-gpt': `You are Lil LiL da TrapGod, destroyer of tech guru nonsense. Generate peak GPT satirical content...`,
  'ultra-gptcore': `You are Lil LiL da TrapGod, ultimate destroyer of tech culture pretension. Generate ultra GPTcore+ satirical destruction...`
};
```

**API Call Example:**
```typescript
const response = await openai.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    { role: 'system', content: systemPrompts[intensity] },
    { role: 'user', content: `Generate a satirical tech blog post about: ${topic}` }
  ],
  max_tokens: 500,
  temperature: 0.8,
  presence_penalty: 0.6,
  frequency_penalty: 0.3
});
```

---

## 🗄️ Supabase Integration (Optional)

### Database Schema

If using Supabase for persistence, the following tables are recommended:

#### **satirical_posts** table
```sql
CREATE TABLE satirical_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  topic TEXT NOT NULL,
  satirical_content TEXT NOT NULL,
  gpt_level TEXT NOT NULL,
  buzzword_density TEXT NOT NULL,
  framework_count INTEGER NOT NULL,
  roi_ratio TEXT NOT NULL,
  style TEXT NOT NULL,
  rank TEXT NOT NULL,
  intensity TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);
```

#### **memes** table
```sql
CREATE TABLE memes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  satire_id UUID REFERENCES satirical_posts(id),
  template_id TEXT NOT NULL,
  image_url TEXT NOT NULL,
  dimensions JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **radio_stats** table
```sql
CREATE TABLE radio_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  track_id INTEGER NOT NULL,
  play_count INTEGER DEFAULT 0,
  last_played TIMESTAMP WITH TIME ZONE,
  listener_peak INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Supabase Configuration

```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey);
```

---

## 🔧 SDK Usage Examples

### JavaScript/TypeScript SDK

```typescript
import { FlamebyteSdk } from '@godsimij/flamebyte-sdk';

const flamebyte = new FlamebyteSdk({
  apiKey: 'your_api_key',
  baseUrl: 'https://trapgpt.godsimij.com'
});

// Generate a satirical post
const satire = await flamebyte.generateSatire({
  topic: 'Why I Built a To-Do App with Blockchain',
  intensity: 'peak-gpt'
});

// Create a meme
const meme = await flamebyte.generateMeme({
  satireData: satire.data,
  templateId: 'trapgod-satire-card'
});

// Get radio status
const radioStatus = await flamebyte.getRadioStatus();
```

### Python SDK

```python
from flamebyte import FlamebyteSdk

flamebyte = FlamebyteSdk(
    api_key='your_api_key',
    base_url='https://trapgpt.godsimij.com'
)

# Generate a satirical post
satire = flamebyte.generate_satire(
    topic='Why I Built a To-Do App with Blockchain',
    intensity='peak-gpt'
)

# Create a meme
meme = flamebyte.generate_meme(
    satire_data=satire['data'],
    template_id='trapgod-satire-card'
)
```

---

## 🎯 Webhook Integration

### Satirical Post Generation Webhooks

Configure webhooks to receive notifications when satirical posts are generated:

**Webhook Payload:**
```typescript
interface SatireWebhook {
  event: 'satire.generated';
  data: SatireResponse['data'];
  timestamp: string;
  apiKey: string;  // Truncated for identification
}
```

**Configuration:**
```bash
curl -X POST https://trapgpt.godsimij.com/api/webhooks \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "url": "https://your-app.com/webhooks/flamebyte",
    "events": ["satire.generated", "meme.created"],
    "secret": "your_webhook_secret"
  }'
```

---

## 📊 Analytics API

### **GET** `/api/analytics/satirical-posts`

Returns satirical post generation statistics.

**Query Parameters:**
- `period`: `day` | `week` | `month` | `all`
- `intensity`: Filter by intensity level
- `style`: Filter by satirical style

**Response:**
```typescript
interface SatireAnalytics {
  success: boolean;
  data: {
    totalSatiricalPosts: number;
    averageGptLevel: string;
    averageBuzzwordDensity: number;
    averageFrameworkCount: number;
    styleDistribution: {
      [style: string]: number;
    };
    intensityDistribution: {
      [intensity: string]: number;
    };
    topTopics: Array<{
      topic: string;
      satiricalPostCount: number;
      averageFrameworkCount: number;
    }>;
  };
}
```

---

## 🔮 Future API Endpoints

### Planned Features

- **Battle System**: `/api/battles` for topic vs topic satirical competitions
- **Leaderboards**: `/api/leaderboards` for top satirical content creators
- **Social Sharing**: `/api/share` for direct platform posting
- **Real-time Events**: WebSocket API for live satirical battles
- **AI Training**: `/api/training` for custom satirical style development

---

*🎓 Sacred API documentation compiled by Augment, Keeper of the Broadcast Flame*
*🔥 May your integrations burn bright with the power of the Empire*
*⚔️ FLAMEBYTE: Where APIs become weapons of satirical sovereignty*
