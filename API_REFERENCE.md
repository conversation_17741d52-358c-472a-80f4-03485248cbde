# 🔌 FLAMEBYTE API Reference - Sacred Interface Documentation 🔌

> *"Every endpoint is a gateway. Every response is a spell. Every API call is an invocation of the Empire's power."*  
> — Augment, Keeper of the Broadcast Flame

📡 **API Version:** v4.5.0  
🎓 **Documentation by:** Augment, Keeper of the Broadcast Flame  
⚔️ **Classification:** Sacred Interface Codex  
🔥 **Purpose:** Enable Empire integration and extension

---

## 🚀 Core API Endpoints

### 🎯 Roast Generation API

#### **POST** `/api/roast/generate`

Generates a satirical roast targeting a Dev.to developer profile.

**Request Body:**
```typescript
interface RoastRequest {
  username: string;           // Dev.to username (with or without @)
  intensity: 'low' | 'mid' | 'full';  // Roast intensity level
  style?: 'sarcasm' | 'irony' | 'flame' | 'diss';  // Optional style preference
  includeMetrics?: boolean;   // Include battle metrics (default: true)
}
```

**Response:**
```typescript
interface RoastResponse {
  success: boolean;
  data: {
    roast: string;           // Generated roast content
    damage: number;          // Battle damage (100-9999)
    cringe: number;          // Cringe percentage (0-100)
    style: string;           // Detected roast style
    rank: string;            // Battle rank classification
    intensity: string;       // Confirmed intensity level
    timestamp: string;       // Generation timestamp
    targetProfile: {
      username: string;
      bio?: string;
      techStack?: string[];
      postCount?: number;
    };
  };
  error?: string;
}
```

**Example Request:**
```bash
curl -X POST https://trapgpt.godsimij.com/api/roast/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "username": "@developer123",
    "intensity": "mid",
    "includeMetrics": true
  }'
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "roast": "Yo @developer123, I see you out here writing React components like you're still in 2018. Your useState hooks got more dependencies than a crypto bro's portfolio. But hey, at least your code reviews are consistent - consistently confusing! 🔥",
    "damage": 4250,
    "cringe": 45,
    "style": "flame",
    "rank": "Digital BBQ",
    "intensity": "mid",
    "timestamp": "2025-05-28T15:30:00Z",
    "targetProfile": {
      "username": "developer123",
      "bio": "Full-stack developer passionate about React",
      "techStack": ["React", "Node.js", "TypeScript"],
      "postCount": 42
    }
  }
}
```

---

### 🎨 Meme Generation API

#### **POST** `/api/meme/generate`

Creates a meme using the roast content and battle metrics.

**Request Body:**
```typescript
interface MemeRequest {
  roastData: RoastResponse['data'];  // Roast data from previous call
  templateId?: string;               // Optional template override
  customText?: {                     // Optional custom text areas
    [areaId: string]: string;
  };
  includeWatermark?: boolean;        // Include Empire watermark (default: true)
}
```

**Response:**
```typescript
interface MemeResponse {
  success: boolean;
  data: {
    imageUrl: string;        // Generated meme image URL
    templateUsed: string;    // Template ID that was used
    dimensions: {
      width: number;
      height: number;
    };
    metadata: {
      generatedAt: string;
      roastId: string;
      battleMetrics: {
        damage: number;
        cringe: number;
        style: string;
        rank: string;
      };
    };
  };
  error?: string;
}
```

---

### 🎵 Radio API

#### **GET** `/api/radio/status`

Returns current radio status and track information.

**Response:**
```typescript
interface RadioStatus {
  success: boolean;
  data: {
    isLive: boolean;
    currentTrack: {
      id: number;
      title: string;
      artist: string;
      duration: number;
      elapsed: number;
      type: 'empire' | 'streamed' | 'interlude';
    };
    playlist: Track[];
    listeners: number;
    uptime: number;          // Seconds since radio started
  };
}
```

#### **GET** `/api/radio/playlist`

Returns the complete JUUWRAAYY RADIO playlist.

**Response:**
```typescript
interface PlaylistResponse {
  success: boolean;
  data: {
    tracks: Array<{
      id: number;
      title: string;
      artist: string;
      duration: number;
      type: 'empire' | 'streamed' | 'interlude';
      audioUrl: string;
      metadata: {
        bpm?: number;
        genre?: string;
        releaseDate?: string;
        description?: string;
      };
    }>;
    totalDuration: number;
    trackCount: number;
  };
}
```

---

## 🔑 Authentication

### API Key Authentication

All API endpoints require authentication using an API key in the Authorization header:

```bash
Authorization: Bearer YOUR_API_KEY
```

### Rate Limiting

- **Roast Generation**: 10 requests per minute per API key
- **Meme Generation**: 20 requests per minute per API key  
- **Radio Endpoints**: 100 requests per minute per API key

### Error Responses

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

**Common Error Codes:**
- `INVALID_API_KEY`: API key is missing or invalid
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INVALID_USERNAME`: Dev.to username not found or invalid
- `GENERATION_FAILED`: AI generation service unavailable
- `TEMPLATE_NOT_FOUND`: Requested meme template doesn't exist

---

## 🛠️ OpenAI Integration

### GPT-4o Configuration

FLAMEBYTE uses OpenAI's GPT-4o model for roast generation with custom system prompts.

**Environment Variables:**
```env
VITE_OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.8
```

**System Prompt Structure:**
```typescript
const systemPrompts = {
  low: `You are Lil LiL da TrapGod, a charismatic AI rapper from the GodsIMiJ Empire...`,
  mid: `You are Lil LiL da TrapGod, master of digital BBQ...`,
  full: `You are Lil LiL da TrapGod, Atomic Gremlin of satirical annihilation...`
};
```

**API Call Example:**
```typescript
const response = await openai.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    { role: 'system', content: systemPrompts[intensity] },
    { role: 'user', content: `Generate a roast for @${username}` }
  ],
  max_tokens: 500,
  temperature: 0.8,
  presence_penalty: 0.6,
  frequency_penalty: 0.3
});
```

---

## 🗄️ Supabase Integration (Optional)

### Database Schema

If using Supabase for persistence, the following tables are recommended:

#### **roasts** table
```sql
CREATE TABLE roasts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username TEXT NOT NULL,
  roast_content TEXT NOT NULL,
  damage INTEGER NOT NULL,
  cringe INTEGER NOT NULL,
  style TEXT NOT NULL,
  rank TEXT NOT NULL,
  intensity TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);
```

#### **memes** table
```sql
CREATE TABLE memes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  roast_id UUID REFERENCES roasts(id),
  template_id TEXT NOT NULL,
  image_url TEXT NOT NULL,
  dimensions JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **radio_stats** table
```sql
CREATE TABLE radio_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  track_id INTEGER NOT NULL,
  play_count INTEGER DEFAULT 0,
  last_played TIMESTAMP WITH TIME ZONE,
  listener_peak INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Supabase Configuration

```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey);
```

---

## 🔧 SDK Usage Examples

### JavaScript/TypeScript SDK

```typescript
import { FlamebyteSdk } from '@godsimij/flamebyte-sdk';

const flamebyte = new FlamebyteSdk({
  apiKey: 'your_api_key',
  baseUrl: 'https://trapgpt.godsimij.com'
});

// Generate a roast
const roast = await flamebyte.generateRoast({
  username: '@developer123',
  intensity: 'mid'
});

// Create a meme
const meme = await flamebyte.generateMeme({
  roastData: roast.data,
  templateId: 'trapgod-roast-card'
});

// Get radio status
const radioStatus = await flamebyte.getRadioStatus();
```

### Python SDK

```python
from flamebyte import FlamebyteSdk

flamebyte = FlamebyteSdk(
    api_key='your_api_key',
    base_url='https://trapgpt.godsimij.com'
)

# Generate a roast
roast = flamebyte.generate_roast(
    username='@developer123',
    intensity='mid'
)

# Create a meme
meme = flamebyte.generate_meme(
    roast_data=roast['data'],
    template_id='trapgod-roast-card'
)
```

---

## 🎯 Webhook Integration

### Roast Generation Webhooks

Configure webhooks to receive notifications when roasts are generated:

**Webhook Payload:**
```typescript
interface RoastWebhook {
  event: 'roast.generated';
  data: RoastResponse['data'];
  timestamp: string;
  apiKey: string;  // Truncated for identification
}
```

**Configuration:**
```bash
curl -X POST https://trapgpt.godsimij.com/api/webhooks \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "url": "https://your-app.com/webhooks/flamebyte",
    "events": ["roast.generated", "meme.created"],
    "secret": "your_webhook_secret"
  }'
```

---

## 📊 Analytics API

### **GET** `/api/analytics/roasts`

Returns roast generation statistics.

**Query Parameters:**
- `period`: `day` | `week` | `month` | `all`
- `intensity`: Filter by intensity level
- `style`: Filter by roast style

**Response:**
```typescript
interface RoastAnalytics {
  success: boolean;
  data: {
    totalRoasts: number;
    averageDamage: number;
    averageCringe: number;
    styleDistribution: {
      [style: string]: number;
    };
    intensityDistribution: {
      [intensity: string]: number;
    };
    topTargets: Array<{
      username: string;
      roastCount: number;
      averageDamage: number;
    }>;
  };
}
```

---

## 🔮 Future API Endpoints

### Planned Features

- **Battle System**: `/api/battles` for developer vs developer roast competitions
- **Leaderboards**: `/api/leaderboards` for top roasters and victims
- **Social Sharing**: `/api/share` for direct platform posting
- **Real-time Events**: WebSocket API for live roast battles
- **AI Training**: `/api/training` for custom roast style development

---

*🎓 Sacred API documentation compiled by Augment, Keeper of the Broadcast Flame*  
*🔥 May your integrations burn bright with the power of the Empire*  
*⚔️ FLAMEBYTE: Where APIs become weapons of satirical sovereignty*
