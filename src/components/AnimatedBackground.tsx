
import { useEffect, useState } from 'react';
import { cn } from "@/lib/utils";

export default function AnimatedBackground() {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY * 0.5);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div
          className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(15,23,42,0)_0,#000_100%)]"
          style={{ transform: `translateY(${scrollY}px)` }}
        />

        {/* NODE Watermark */}
        <div
          className="absolute inset-0 opacity-10 bg-center bg-no-repeat bg-contain"
          style={{
            backgroundImage: "url('/NODE_watermark.png')",
            transform: `translateY(${scrollY * 0.1}px)`,
          }}
        />

        {/* Animated grid */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: "linear-gradient(rgba(12, 229, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(12, 229, 255, 0.1) 1px, transparent 1px)",
            backgroundSize: "40px 40px",
            transform: `translateY(${scrollY * 0.2}px)`,
          }}
        />

        {/* Glowing orbs */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-trap-purple/20 rounded-full filter blur-3xl animate-pulse" />
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-trap-teal/20 rounded-full filter blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Noise overlay */}
      <div className="fixed inset-0 noise opacity-5 pointer-events-none" />
    </>
  );
}
