import React, { useState, useEffect } from "react";
import { useChatMode } from "@/contexts/ChatModeContext";
import { nanoid } from "nanoid";
import { cn } from "@/lib/utils";
import { ArrowLeft } from "lucide-react";
import Topbar from "./chat/Topbar";
import ChatLog, { Message } from "./chat/ChatLog";
import ChatInput from "./chat/ChatInput";
import { Button } from "./ui/button";
import { useDeviceId } from "@/hooks/useDeviceId";
import { supabase } from "@/integrations/supabase/client";
// import { routeGremlinRequest } from "@/lib/gremlinRouter";

export default function ChatView() {
  const { mode, setMode, setStartupComplete, aiProvider } = useChatMode();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const deviceId = useDeviceId();

  useEffect(() => {
    if (deviceId) {
      const loadMessages = async () => {
        const { data, error } = await supabase
          .from('device_chat_messages')
          .select('*')
          .eq('device_id', deviceId)
          .order('created_at', { ascending: true });

        if (!error && data) {
          setMessages(data.map(msg => ({
            id: msg.id,
            content: msg.content,
            type: msg.type as "user" | "ai"
          })));
        }
      };

      loadMessages();
    }
  }, [deviceId]);

  const handleBackToShrine = () => {
    setStartupComplete(false);
    setMode(null);
  };

  // Simple response generator (temporary fallback)
  const generateSimpleResponse = (userMessage: string, mode: ChatMode): string => {
    const lowercaseMsg = userMessage.toLowerCase();

    switch (mode) {
      case "OG":
        return `JUUURAYYY! You in OG mode now! ${
          lowercaseMsg.includes("help") ? "I gotchu fam, whatchu need help with?" :
          lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Yo, what's good!" :
          `Keep it 100 with ya. You asked about "${userMessage}" and I'm vibin' with that question!`
        }`;

      case "GODMODE":
        return `ABSOLUTE POWER INITIATED. ${
          lowercaseMsg.includes("help") ? "Your request shall be granted with omniscient precision." :
          lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "I HAVE AWAITED YOUR ARRIVAL, MORTAL." :
          `Your inquiry regarding "${userMessage}" is but a trivial matter for one with unlimited knowledge.`
        }`;

      case "ROAST":
        return `OH LOOK WHO'S ASKING QUESTIONS NOW! ${
          lowercaseMsg.includes("help") ? "You need help? Have you tried, I don't know, GOOGLE?" :
          lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Wow, so original with that greeting. Impressed." :
          `"${userMessage}"? That's what you're asking? Next time bring some REAL questions!`
        }`;

      case "STEP":
        return `Let me break this down clearly for you. ${
          lowercaseMsg.includes("help") ? "First, I'll identify what you need. Second, I'll explain how to solve it. Third, I'll verify your understanding." :
          lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Step 1: Greeting acknowledged. Step 2: Establishing communication channel. Step 3: Ready for your detailed query." :
          `Regarding "${userMessage}": Step 1 - Analyzing your question. Step 2 - Processing relevant information. Step 3 - Formulating precise response.`
        }`;

      case "RAW":
        return `${
          lowercaseMsg.includes("help") ? "Help request registered. Specify requirements immediately." :
          lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Greeting unnecessary. State purpose." :
          `Input: "${userMessage}". Processing. Direct response follows. No embellishment.`
        }`;

      default:
        return `JUUURAYYY! You in ${mode} mode saying: "${userMessage}"`;
    }
  };

  const handleSendMessage = async (content: string) => {
    if (!deviceId) return;

    const userMessage: Message = {
      id: nanoid(),
      content,
      type: "user",
    };

    await supabase.from('device_chat_messages').insert({
      device_id: deviceId,
      content: userMessage.content,
      type: userMessage.type,
      mode: mode || 'default'
    });

    setMessages(prev => [...prev, userMessage]);
    setHasInteracted(true);

    setIsLoading(true);
    const thinkingTime =
      mode === "GODMODE" ? 500 :
      mode === "RAW" ? 300 :
      mode === "STEP" ? 1200 :
      800;

    setTimeout(async () => {
      try {
        // Use simple response generator for now
        const aiResponse = generateSimpleResponse(content, mode!);

        const aiMessage: Message = {
          id: nanoid(),
          content: aiResponse,
          type: "ai",
        };

        await supabase.from('device_chat_messages').insert({
          device_id: deviceId,
          content: aiMessage.content,
          type: aiMessage.type,
          mode: mode || 'default'
        });

        setMessages(prev => [...prev, aiMessage]);
      } catch (error) {
        console.error('Failed to generate response:', error);

        // Emergency fallback
        const fallbackMessage: Message = {
          id: nanoid(),
          content: "JUUURAYYY! Something glitched in the matrix, but I'm still here! Try again, fam.",
          type: "ai",
        };

        setMessages(prev => [...prev, fallbackMessage]);
      }

      setIsLoading(false);
    }, thinkingTime);
  };

  const modeGlowStyles = {
    OG: "shadow-[0_0_15px_rgba(57,255,20,0.3)] hover:shadow-[0_0_25px_rgba(57,255,20,0.5)] text-trap-green",
    GODMODE: "shadow-[0_0_15px_rgba(255,255,0,0.3)] hover:shadow-[0_0_25px_rgba(255,255,0,0.5)] text-trap-yellow",
    ROAST: "shadow-[0_0_15px_rgba(255,0,0,0.3)] hover:shadow-[0_0_25px_rgba(255,0,0,0.5)] text-red-500",
    STEP: "shadow-[0_0_15px_rgba(0,149,255,0.3)] hover:shadow-[0_0_25px_rgba(0,149,255,0.5)] text-blue-500",
    RAW: "shadow-[0_0_15px_rgba(155,135,245,0.3)] hover:shadow-[0_0_25px_rgba(155,135,245,0.5)] text-trap-purple",
  };

  return (
    <div className={cn(
      "flex flex-col min-h-screen bg-background relative overflow-hidden",
      "before:content-[''] before:absolute before:inset-0",
      "before:bg-[radial-gradient(circle_at_center,rgba(15,23,42,0)_0,#000_100%)]",
      "before:pointer-events-none before:z-0"
    )}>
      <div className="absolute inset-0 noise opacity-10" />
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: "linear-gradient(rgba(11, 27, 47, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(11, 27, 47, 0.3) 1px, transparent 1px)",
          backgroundSize: "40px 40px"
        }}
      />

      <Button
        onClick={handleBackToShrine}
        className={cn(
          "fixed top-4 left-4 z-50 font-future gap-2",
          "bg-background/20 backdrop-blur-sm border border-muted/50",
          "transition-all duration-300 hover:scale-105",
          !hasInteracted ? "animate-pulse hover:animate-none" : "",
          mode && modeGlowStyles[mode],
          "md:px-6 md:py-3",
          "px-4 py-2"
        )}
      >
        <ArrowLeft className="w-4 h-4" />
        <span className="hidden md:inline">Back to Shrine</span>
        <span className="md:hidden">Shrine</span>
      </Button>

      <Topbar mode={mode!} />
      <ChatLog messages={messages} isLoading={isLoading} />
      <ChatInput onSubmit={handleSendMessage} isLoading={isLoading} />
    </div>
  );
}
