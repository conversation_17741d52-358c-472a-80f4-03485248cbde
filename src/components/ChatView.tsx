import React, { useState, useEffect } from "react";
import { useChatMode } from "@/contexts/ChatModeContext";
import { nanoid } from "nanoid";
import { cn } from "@/lib/utils";
import { ArrowLeft } from "lucide-react";
import Topbar from "./chat/Topbar";
import ChatLog, { Message } from "./chat/ChatLog";
import ChatInput from "./chat/ChatInput";
import RadioWidget from "./radio/RadioWidget";
import { Button } from "./ui/button";
import { useDeviceId } from "@/hooks/useDeviceId";
import { supabase } from "@/integrations/supabase/client";
import { routeGremlinRequest } from "@/lib/gremlinRouter";

export default function ChatView() {
  const { mode, setStartupComplete, aiProvider } = useChatMode();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const deviceId = useDeviceId();

  useEffect(() => {
    if (deviceId) {
      const loadMessages = async () => {
        const { data, error } = await supabase
          .from('device_chat_messages')
          .select('*')
          .eq('device_id', deviceId)
          .order('created_at', { ascending: true });

        if (!error && data) {
          setMessages(data.map(msg => ({
            id: msg.id,
            content: msg.content,
            type: msg.type as "user" | "ai"
          })));
        }
      };

      loadMessages();
    }
  }, [deviceId]);

  const handleBackToShrine = () => {
    setStartupComplete(false);
    setMode(null);
  };

  // Convert messages to conversation history format for OpenAI
  const getConversationHistory = () => {
    return messages.map(msg => ({
      role: msg.type === 'user' ? 'user' as const : 'assistant' as const,
      content: msg.content
    }));
  };

  const handleSendMessage = async (content: string) => {
    if (!deviceId) return;

    const userMessage: Message = {
      id: nanoid(),
      content,
      type: "user",
    };

    await supabase.from('device_chat_messages').insert({
      device_id: deviceId,
      content: userMessage.content,
      type: userMessage.type,
      mode: mode || 'default'
    });

    setMessages(prev => [...prev, userMessage]);
    setHasInteracted(true);

    setIsLoading(true);
    const thinkingTime = 800; // OG mode thinking time

    setTimeout(async () => {
      try {
        // Get conversation history for context
        const conversationHistory = getConversationHistory();

        // Use the gremlin router to get AI response
        const response = await routeGremlinRequest(
          content,
          mode!,
          { provider: aiProvider },
          conversationHistory
        );

        const aiMessage: Message = {
          id: nanoid(),
          content: response.content,
          type: "ai",
        };

        await supabase.from('device_chat_messages').insert({
          device_id: deviceId,
          content: aiMessage.content,
          type: aiMessage.type,
          mode: mode || 'default'
        });

        setMessages(prev => [...prev, aiMessage]);

        // Log if response came from API or fallback
        if (response.isFromAPI) {
          console.log('✅ Response from AI API');
        } else {
          console.log('⚠️ Using fallback response:', response.error);
        }

      } catch (error) {
        console.error('Failed to generate response:', error);

        // Emergency fallback
        const fallbackMessage: Message = {
          id: nanoid(),
          content: "JUUURAYYY! Something glitched in the matrix, but I'm still here! Try again, fam.",
          type: "ai",
        };

        await supabase.from('device_chat_messages').insert({
          device_id: deviceId,
          content: fallbackMessage.content,
          type: fallbackMessage.type,
          mode: mode || 'default'
        });

        setMessages(prev => [...prev, fallbackMessage]);
      }

      setIsLoading(false);
    }, thinkingTime);
  };

  const ogGlowStyle = "shadow-[0_0_15px_rgba(57,255,20,0.3)] hover:shadow-[0_0_25px_rgba(57,255,20,0.5)] text-trap-green";

  return (
    <div className={cn(
      "flex flex-col min-h-screen bg-background relative overflow-hidden",
      "before:content-[''] before:absolute before:inset-0",
      "before:bg-[radial-gradient(circle_at_center,rgba(15,23,42,0)_0,#000_100%)]",
      "before:pointer-events-none before:z-0"
    )}>
      <div className="absolute inset-0 noise opacity-10" />
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: "linear-gradient(rgba(11, 27, 47, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(11, 27, 47, 0.3) 1px, transparent 1px)",
          backgroundSize: "40px 40px"
        }}
      />

      <Button
        onClick={handleBackToShrine}
        className={cn(
          "fixed top-4 left-4 z-50 font-future gap-3",
          "bg-background/20 backdrop-blur-sm border border-muted/50",
          "transition-all duration-300 hover:scale-105",
          !hasInteracted ? "animate-pulse hover:animate-none" : "",
          ogGlowStyle,
          "md:px-8 md:py-4 text-lg md:text-xl",
          "px-6 py-3 text-base"
        )}
      >
        <ArrowLeft className="w-5 h-5 md:w-6 md:h-6" />
        <span className="hidden md:inline">Back to Shrine</span>
        <span className="md:hidden">Shrine</span>
      </Button>

      <Topbar mode={mode!} />
      <ChatLog messages={messages} isLoading={isLoading} />
      <ChatInput onSubmit={handleSendMessage} isLoading={isLoading} />

      {/* Radio Widget - Always visible in chat */}
      <RadioWidget />
    </div>
  );
}
