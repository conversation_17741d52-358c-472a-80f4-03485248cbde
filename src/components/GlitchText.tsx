
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

interface GlitchTextProps {
  text: string;
  className?: string;
  delay?: number;
}

export default function GlitchText({ text, className, delay = 0 }: GlitchTextProps) {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    
    return () => clearTimeout(timer);
  }, [delay]);
  
  if (!isVisible) return null;
  
  return (
    <div className={cn("relative font-trap", className)}>
      {/* Base text */}
      <div className="relative z-10 text-glow text-trap-teal animate-glitch1">
        {text}
      </div>
      
      {/* Glitch layers */}
      <div className="absolute inset-0 text-trap-purple animate-glitch2 opacity-50">
        {text}
      </div>
      <div className="absolute inset-0 text-trap-pink animate-glitch3 opacity-50">
        {text}
      </div>
    </div>
  );
}
