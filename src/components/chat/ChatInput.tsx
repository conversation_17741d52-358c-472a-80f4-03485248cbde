
import { useState, useRef, useEffect } from "react";
import { Flame, Mic, Send, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useChatMode } from "@/contexts/ChatModeContext";
import { Button } from "@/components/ui/button";

interface ChatInputProps {
  onSubmit: (message: string) => void;
  isLoading?: boolean;
}

export default function ChatInput({ onSubmit, isLoading }: ChatInputProps) {
  const [input, setInput] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { mode } = useChatMode();

  useEffect(() => {
    // Auto focus the input when component mounts
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      onSubmit(input.trim());
      setInput("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle Shift+Enter for new line
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <form 
      onSubmit={handleSubmit}
      className={cn(
        "flex items-center gap-2 p-4",
        "border-t border-muted/20 bg-background/80 backdrop-blur-sm",
        "relative z-10 shadow-lg"
      )}
    >
      <div className="relative flex-1 group">
        <Input
          ref={inputRef}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyDown={handleKeyDown}
          placeholder="Type ya message..."
          className={cn(
            "flex-1 bg-background/40 border-muted/50 text-foreground pr-10",
            "backdrop-blur-sm transition-all duration-300",
            "focus:scale-[1.01]",
            isFocused && mode === "OG" && "focus:shadow-[0_0_10px_rgba(57,255,20,0.3)]",
            isFocused && mode === "GODMODE" && "focus:shadow-[0_0_10px_rgba(255,255,0,0.3)]",
            isFocused && mode === "ROAST" && "focus:shadow-[0_0_10px_rgba(255,0,0,0.3)]",
            isFocused && mode === "STEP" && "focus:shadow-[0_0_10px_rgba(0,149,255,0.3)]",
            isFocused && mode === "RAW" && "focus:shadow-[0_0_10px_rgba(155,135,245,0.3)]",
            !isFocused && "focus:shadow-[0_0_10px_rgba(12,229,255,0.2)]"
          )}
          disabled={isLoading}
        />
        
        {input && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-70 hover:opacity-100"
            onClick={() => setInput("")}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      
      <Button
        type="button"
        disabled={isLoading}
        className={cn(
          "p-2 rounded-full transition-all duration-300",
          "bg-background/20 border border-muted/30",
          "hover:bg-background/40"
        )}
      >
        <Mic className={cn(
          "w-5 h-5",
          mode === "OG" && "text-trap-green",
          mode === "GODMODE" && "text-trap-yellow",
          mode === "ROAST" && "text-red-500",
          mode === "STEP" && "text-blue-500",
          mode === "RAW" && "text-trap-purple"
        )} />
      </Button>
      
      <button
        type="submit"
        disabled={isLoading || !input.trim()}
        className={cn(
          "p-2 rounded-full transition-all duration-300",
          "holographic hover:scale-110",
          "focus:outline-none focus:ring-2 focus:ring-primary",
          "disabled:opacity-50 disabled:hover:scale-100",
          "bg-background/20 border border-muted/30"
        )}
      >
        {input.trim() ? (
          <Send className={cn(
            "w-5 h-5",
            mode === "OG" && "text-trap-green animate-pulse",
            mode === "GODMODE" && "text-trap-yellow animate-pulse",
            mode === "ROAST" && "text-red-500 animate-pulse",
            mode === "STEP" && "text-blue-500 animate-pulse",
            mode === "RAW" && "text-trap-purple animate-pulse"
          )} />
        ) : (
          <Flame className={cn(
            "w-5 h-5",
            mode === "OG" && "text-trap-green animate-pulse",
            mode === "GODMODE" && "text-trap-yellow animate-pulse",
            mode === "ROAST" && "text-red-500 animate-pulse",
            mode === "STEP" && "text-blue-500 animate-pulse",
            mode === "RAW" && "text-trap-purple animate-pulse"
          )} />
        )}
      </button>
    </form>
  );
}
