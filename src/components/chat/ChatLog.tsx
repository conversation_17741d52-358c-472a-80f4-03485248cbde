
import { useEffect, useRef } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import ChatMessage from "./ChatMessage";
import { cn } from "@/lib/utils";
import { useChatMode } from "@/contexts/ChatModeContext";

export interface Message {
  id: string;
  content: string;
  type: "user" | "ai";
}

interface ChatLogProps {
  messages: Message[];
  isLoading?: boolean;
}

export default function ChatLog({ messages, isLoading }: ChatLogProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const { mode } = useChatMode();

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isLoading]);

  return (
    <ScrollArea className={cn(
      "flex-1 w-full p-4 relative",
      "before:content-[''] before:absolute before:inset-0",
      "before:bg-[linear-gradient(0deg,rgba(0,0,0,0.2)_0%,transparent_10%,transparent_90%,rgba(0,0,0,0.2)_100%)]",
      "before:pointer-events-none before:z-10"
    )}>
      <div className="space-y-4 relative">
        <div className="absolute inset-0 noise opacity-50 pointer-events-none" />

        {/* Welcome Message when no messages */}
        {messages.length === 0 && (
          <div className={cn(
            "flex flex-col items-center justify-center min-h-[50vh] gap-6",
            "text-center p-8 md:p-12 rounded-lg",
            "bg-background/20 backdrop-blur-sm border border-muted/30",
            "animate-fade-in"
          )}>
            <div className="text-4xl md:text-5xl lg:text-6xl font-trap text-trap-green">
              WELCOME TO OG MODE
            </div>
            <p className="text-muted-foreground max-w-2xl text-lg md:text-xl lg:text-2xl leading-relaxed">
              Original trap mode. Keep it real and get ya answers straight from da TrapGod Gremlin himself.
            </p>
            <div className="text-lg md:text-xl opacity-70 mt-4 font-future">Type your message below to start chattin'</div>
          </div>
        )}

        {messages.map((message) => (
          <ChatMessage
            key={message.id}
            content={message.content}
            type={message.type}
          />
        ))}

        {isLoading && (
          <ChatMessage
            content="Processing ya request, hold up..."
            type="ai"
            isLoading
          />
        )}

        <div ref={scrollRef} />
      </div>
    </ScrollArea>
  );
}
