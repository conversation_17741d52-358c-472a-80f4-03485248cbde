
import { cn } from "@/lib/utils";
import { ChatMode } from "@/contexts/ChatModeContext";

const modeStyles: Record<ChatMode, string> = {
  OG: "shadow-[0_0_15px_rgba(57,255,20,0.3)] text-trap-green",
  GODMODE: "shadow-[0_0_15px_rgba(255,255,0,0.3)] text-trap-yellow",
  ROAST: "shadow-[0_0_15px_rgba(255,0,0,0.3)] text-red-500",
  STEP: "shadow-[0_0_15px_rgba(0,149,255,0.3)] text-blue-500",
  RAW: "shadow-[0_0_15px_rgba(155,135,245,0.3)] text-trap-purple",
};

interface TopbarProps {
  mode: ChatMode;
}

export default function Topbar({ mode }: TopbarProps) {
  return (
    <div 
      className={cn(
        "sticky top-0 z-40 w-full p-4 pl-24 md:pl-32", // Added left padding to make room for the button
        "bg-background/80 backdrop-blur-sm border-b border-muted",
        modeStyles[mode]
      )}
    >
      <div className="flex items-center justify-center">
        <h1 className="text-2xl font-trap animate-pulse">
          TRAPGPT :: {mode} MODE
        </h1>
      </div>
    </div>
  );
}
