
import { cn } from "@/lib/utils";
import { ChatMode } from "@/contexts/ChatModeContext";

const ogStyle = "shadow-[0_0_15px_rgba(57,255,20,0.3)] text-trap-green";

interface TopbarProps {
  mode: ChatMode;
}

export default function Topbar({ mode }: TopbarProps) {
  return (
    <div
      className={cn(
        "sticky top-0 z-40 w-full p-4 pl-24 md:pl-32", // Added left padding to make room for the button
        "bg-background/80 backdrop-blur-sm border-b border-muted",
        ogStyle
      )}
    >
      <div className="flex items-center justify-center">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-trap animate-pulse">
          TRAPGPT :: OG MODE
        </h1>
      </div>
    </div>
  );
}
