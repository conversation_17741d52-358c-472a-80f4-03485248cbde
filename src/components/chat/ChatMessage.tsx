
import { cn } from "@/lib/utils";
import { useChatMode } from "@/contexts/ChatModeContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useState, useEffect } from "react";

export type MessageType = "user" | "ai";

interface ChatMessageProps {
  content: string;
  type: MessageType;
  isLoading?: boolean;
}

const ogStyle = "shadow-[0_0_15px_rgba(57,255,20,0.3)] text-trap-green";

const ogAvatar = "/avatars/og.png";

export default function ChatMessage({ content, type, isLoading }: ChatMessageProps) {
  const { mode } = useChatMode();
  const [displayText, setDisplayText] = useState("");
  const [isTyping, setIsTyping] = useState(false);

  // Typing animation effect for AI messages
  useEffect(() => {
    if (type === "ai" && !isLoading && content) {
      setIsTyping(true);
      setDisplayText("");

      let index = 0;
      const typeInterval = setInterval(() => {
        if (index < content.length) {
          setDisplayText((prev) => prev + content.charAt(index));
          index++;
        } else {
          clearInterval(typeInterval);
          setIsTyping(false);
        }
      }, 10); // Speed of typing

      return () => clearInterval(typeInterval);
    } else if (type === "user") {
      setDisplayText(content);
    }
  }, [content, type, isLoading]);

  return (
    <div
      className={cn(
        "flex w-full mb-4 animate-fade-in items-end gap-2",
        type === "user" ? "justify-end flex-row-reverse" : "justify-start"
      )}
    >
      {/* Avatar Section */}
      {type === "ai" ? (
        <Avatar className="w-8 h-8 border-2 border-trap-green animate-pulse">
          <AvatarImage src={ogAvatar} />
          <AvatarFallback className={cn(
            "bg-background/40 backdrop-blur-sm",
            ogStyle
          )}>
            OG
          </AvatarFallback>
        </Avatar>
      ) : (
        <Avatar className="w-8 h-8 border-2 border-primary">
          <AvatarImage src="/avatars/user.png" />
          <AvatarFallback className="bg-primary/20 text-primary">U</AvatarFallback>
        </Avatar>
      )}

      {/* Message Bubble */}
      <div
        className={cn(
          "max-w-[80%] p-6 md:p-8 rounded-lg backdrop-blur-sm",
          "transition-all duration-300 hover:scale-[1.02]",
          "text-lg md:text-xl lg:text-2xl leading-relaxed",
          type === "user"
            ? "bg-primary/20 text-primary font-future rounded-tr-none"
            : cn(
                "bg-background/40 border border-muted/50 rounded-tl-none font-trap",
                ogStyle,
                isTyping || isLoading ? "" : "animate-glitch1"
              ),
          isLoading && "animate-pulse"
        )}
      >
        {type === "ai" ? (
          <>
            {isLoading ? (
              "Processing ya request, hold up..."
            ) : (
              <>
                {displayText}
                {isTyping && (
                  <span className="inline-block w-1 h-4 ml-1 bg-current animate-pulse"></span>
                )}
              </>
            )}
          </>
        ) : (
          content
        )}
      </div>
    </div>
  );
}
