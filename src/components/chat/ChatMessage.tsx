
import { cn } from "@/lib/utils";
import { useChatMode } from "@/contexts/ChatModeContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useState, useEffect } from "react";

export type MessageType = "user" | "ai";

interface ChatMessageProps {
  content: string;
  type: MessageType;
  isLoading?: boolean;
}

const modeStyles = {
  OG: "shadow-[0_0_15px_rgba(57,255,20,0.3)] text-trap-green",
  GODMODE: "shadow-[0_0_15px_rgba(255,255,0,0.3)] text-trap-yellow",
  ROAST: "shadow-[0_0_15px_rgba(255,0,0,0.3)] text-red-500",
  STEP: "shadow-[0_0_15px_rgba(0,149,255,0.3)] text-blue-500",
  RAW: "shadow-[0_0_15px_rgba(155,135,245,0.3)] text-trap-purple",
};

const modeAvatars = {
  OG: "/avatars/og.png",
  GODMODE: "/avatars/godmode.png",
  ROAST: "/avatars/roast.png",
  STEP: "/avatars/step.png",
  RAW: "/avatars/raw.png",
};

export default function ChatMessage({ content, type, isLoading }: ChatMessageProps) {
  const { mode } = useChatMode();
  const [displayText, setDisplayText] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  
  // Typing animation effect for AI messages
  useEffect(() => {
    if (type === "ai" && !isLoading && content) {
      setIsTyping(true);
      setDisplayText("");
      
      let index = 0;
      const typeInterval = setInterval(() => {
        if (index < content.length) {
          setDisplayText((prev) => prev + content.charAt(index));
          index++;
        } else {
          clearInterval(typeInterval);
          setIsTyping(false);
        }
      }, 10); // Speed of typing
      
      return () => clearInterval(typeInterval);
    } else if (type === "user") {
      setDisplayText(content);
    }
  }, [content, type, isLoading]);
  
  return (
    <div 
      className={cn(
        "flex w-full mb-4 animate-fade-in items-end gap-2",
        type === "user" ? "justify-end flex-row-reverse" : "justify-start"
      )}
    >
      {/* Avatar Section */}
      {type === "ai" ? (
        <Avatar className={cn(
          "w-8 h-8 border-2", 
          mode && `border-${modeStyles[mode].split(" ")[1]}`,
          "animate-pulse"
        )}>
          <AvatarImage src={mode ? modeAvatars[mode] : ""} />
          <AvatarFallback className={cn(
            "bg-background/40 backdrop-blur-sm",
            mode && modeStyles[mode]
          )}>
            {mode?.charAt(0) || "T"}
          </AvatarFallback>
        </Avatar>
      ) : (
        <Avatar className="w-8 h-8 border-2 border-primary">
          <AvatarImage src="/avatars/user.png" />
          <AvatarFallback className="bg-primary/20 text-primary">U</AvatarFallback>
        </Avatar>
      )}
      
      {/* Message Bubble */}
      <div 
        className={cn(
          "max-w-[80%] p-4 rounded-lg backdrop-blur-sm",
          "transition-all duration-300 hover:scale-[1.02]",
          type === "user" 
            ? "bg-primary/20 text-primary font-future rounded-tr-none" 
            : cn(
                "bg-background/40 border border-muted/50 rounded-tl-none font-trap",
                mode && modeStyles[mode],
                isTyping || isLoading ? "" : "animate-glitch1"
              ),
          isLoading && "animate-pulse"
        )}
      >
        {type === "ai" ? (
          <>
            {isLoading ? (
              "Processing ya request, hold up..."
            ) : (
              <>
                {displayText}
                {isTyping && (
                  <span className="inline-block w-1 h-4 ml-1 bg-current animate-pulse"></span>
                )}
              </>
            )}
          </>
        ) : (
          content
        )}
      </div>
    </div>
  );
}
