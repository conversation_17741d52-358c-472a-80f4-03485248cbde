import { useState } from 'react';
import { Button } from './ui/button';
import { testOpenAIConnection, generateGremlinResponse } from '@/lib/openai';
import { ChatMode } from '@/contexts/ChatModeContext';

export default function OpenAITest() {
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionResult, setConnectionResult] = useState<boolean | null>(null);
  const [isTestingResponse, setIsTestingResponse] = useState(false);
  const [responseResult, setResponseResult] = useState<string | null>(null);

  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      const result = await testOpenAIConnection();
      setConnectionResult(result);
    } catch (error) {
      console.error('Connection test failed:', error);
      setConnectionResult(false);
    }
    setIsTestingConnection(false);
  };

  const testResponse = async () => {
    setIsTestingResponse(true);
    try {
      const result = await generateGremlinResponse('Hello', 'OG' as ChatMode);
      setResponseResult(result.content);
    } catch (error) {
      console.error('Response test failed:', error);
      setResponseResult('Error: ' + (error as Error).message);
    }
    setIsTestingResponse(false);
  };

  return (
    <div className="p-4 bg-black/20 border border-trap-purple/30 rounded-lg max-w-md">
      <h3 className="text-lg font-future text-trap-teal mb-4">OpenAI Integration Test</h3>
      
      <div className="space-y-4">
        <div>
          <Button 
            onClick={testConnection} 
            disabled={isTestingConnection}
            className="w-full"
          >
            {isTestingConnection ? 'Testing...' : 'Test Connection'}
          </Button>
          {connectionResult !== null && (
            <p className={`mt-2 text-sm ${connectionResult ? 'text-green-400' : 'text-red-400'}`}>
              Connection: {connectionResult ? 'SUCCESS' : 'FAILED'}
            </p>
          )}
        </div>

        <div>
          <Button 
            onClick={testResponse} 
            disabled={isTestingResponse}
            className="w-full"
          >
            {isTestingResponse ? 'Testing...' : 'Test Response'}
          </Button>
          {responseResult && (
            <div className="mt-2 p-2 bg-black/40 rounded text-sm text-gray-300">
              {responseResult}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
