import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  Share2, 
  Download, 
  Flame, 
  Zap, 
  <PERSON>, 
  Trophy,
  Co<PERSON>,
  Bo<PERSON>
} from 'lucide-react';
import { SatireResult } from '@/lib/satireEngine';
import { toast } from '@/components/ui/sonner';

interface SatireCardProps {
  satire: SatireResult;
  memeImageUrl?: string;
  className?: string;
}

export default function SatireCard({ satire, memeImageUrl, className }: SatireCardProps) {
  const [isSharing, setIsSharing] = useState(false);

  // Style configurations based on satirical intensity
  const intensityStyles = {
    'mildly-robotic': {
      border: 'border-orange-500',
      glow: 'shadow-[0_0_20px_rgba(249,115,22,0.3)]',
      bg: 'bg-gradient-to-br from-orange-500/10 to-yellow-500/10',
      icon: Flame,
      color: 'text-orange-500'
    },
    'peak-gpt': {
      border: 'border-red-500',
      glow: 'shadow-[0_0_20px_rgba(239,68,68,0.4)]',
      bg: 'bg-gradient-to-br from-red-500/10 to-pink-500/10',
      icon: Zap,
      color: 'text-red-500'
    },
    'ultra-gptcore': {
      border: 'border-purple-500',
      glow: 'shadow-[0_0_30px_rgba(147,51,234,0.6)]',
      bg: 'bg-gradient-to-br from-purple-500/20 to-pink-500/20',
      icon: Skull,
      color: 'text-purple-500'
    }
  };

  const styleConfig = intensityStyles[satire.intensity];
  const IntensityIcon = styleConfig.icon;

  const handleShare = async () => {
    setIsSharing(true);
    try {
      if (navigator.share) {
        await navigator.share({
          title: `Satirical Tech Post by TrapGPT!`,
          text: satire.satiricalPost,
          url: window.location.href
        });
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(
          `🎭 Satirical tech post generated by Lil LiL da TrapGod! 🎭\n\n"${satire.satiricalPost}"\n\nGPT Level: ${satire.gptLevel} | Frameworks: ${satire.frameworkCount}\n\nGenerate your own at: ${window.location.origin}`
        );
        toast.success('Satirical post copied to clipboard!');
      }
    } catch (error) {
      console.error('Share failed:', error);
      toast.error('Failed to share satirical post');
    }
    setIsSharing(false);
  };

  const handleDownload = () => {
    // In a real implementation, this would generate and download the meme image
    toast.success('Satirical meme download started!');
  };

  const getFrameworkColor = (count: number) => {
    if (count < 20) return 'text-orange-400';
    if (count < 40) return 'text-red-400';
    return 'text-purple-400';
  };

  const getBuzzwordColor = (density: string) => {
    const num = parseInt(density);
    if (num < 5) return 'text-green-400';
    if (num < 10) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className={cn(
      "w-full max-w-2xl mx-auto rounded-lg border-2 backdrop-blur-sm",
      "transition-all duration-300 hover:scale-[1.02]",
      styleConfig.border,
      styleConfig.glow,
      styleConfig.bg,
      className
    )}>
      {/* Card Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <IntensityIcon className={cn("w-6 h-6", styleConfig.color)} />
            <h3 className="text-xl font-future text-white">
              SATIRICAL POST CARD
            </h3>
          </div>
          <div className="flex items-center gap-1">
            <Trophy className="w-4 h-4 text-trap-yellow" />
            <span className="text-sm font-trap text-trap-yellow">
              {satire.rank}
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Bot className="w-12 h-12 p-2 rounded-full border-2 border-trap-teal bg-trap-teal/20 text-trap-teal" />
          <div>
            <h4 className="text-lg font-trap text-trap-teal">
              AI Tech Blogger
            </h4>
            <p className="text-sm text-gray-400">
              {satire.gptLevel}
            </p>
          </div>
        </div>
      </div>

      {/* Meme Image (if available) */}
      {memeImageUrl && (
        <div className="relative">
          <img 
            src={memeImageUrl} 
            alt="Satirical Meme"
            className="w-full h-48 object-cover"
          />
          <div className="absolute top-2 right-2">
            <span className="bg-black/80 text-trap-purple text-xs px-2 py-1 rounded font-future">
              🎭 SATIRIZED BY THE EMPIRE
            </span>
          </div>
        </div>
      )}

      {/* Satirical Post Content */}
      <div className="p-4">
        <div className="mb-4 p-4 bg-black/60 rounded-lg border border-gray-700 max-h-64 overflow-y-auto">
          <p className="text-white font-trap text-base leading-relaxed whitespace-pre-wrap">
            {satire.satiricalPost}
          </p>
        </div>

        {/* Parody Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
          <div className="text-center p-2 bg-black/40 rounded border border-gray-700">
            <div className={cn("text-lg font-future", styleConfig.color)}>
              {satire.gptLevel.split(' ')[0]}
            </div>
            <div className="text-xs text-gray-400 font-trap">
              GPT LEVEL
            </div>
          </div>
          
          <div className="text-center p-2 bg-black/40 rounded border border-gray-700">
            <div className={cn("text-lg font-future", getBuzzwordColor(satire.buzzwordDensity))}>
              {satire.buzzwordDensity}
            </div>
            <div className="text-xs text-gray-400 font-trap">
              BUZZWORDS
            </div>
          </div>
          
          <div className="text-center p-2 bg-black/40 rounded border border-gray-700">
            <div className={cn("text-lg font-future", getFrameworkColor(satire.frameworkCount))}>
              {satire.frameworkCount}
            </div>
            <div className="text-xs text-gray-400 font-trap">
              FRAMEWORKS
            </div>
          </div>

          <div className="text-center p-2 bg-black/40 rounded border border-gray-700">
            <div className="text-sm font-future text-trap-yellow">
              {satire.roiRatio}
            </div>
            <div className="text-xs text-gray-400 font-trap">
              ROI RATIO
            </div>
          </div>
        </div>

        {/* Style Badge */}
        <div className="mb-4 text-center">
          <span className={cn(
            "inline-block px-3 py-1 rounded-full text-sm font-future uppercase border",
            styleConfig.color,
            styleConfig.border.replace('border-', 'border-'),
            styleConfig.bg
          )}>
            {satire.style.replace('-', ' ')}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={handleDownload}
              className="bg-trap-teal/20 hover:bg-trap-teal/40 border border-trap-teal text-trap-teal"
            >
              <Download className="w-4 h-4 mr-2" />
              Save Meme
            </Button>
            
            <Button
              onClick={() => {
                navigator.clipboard.writeText(satire.satiricalPost);
                toast.success('Satirical post copied!');
              }}
              className="bg-trap-purple/20 hover:bg-trap-purple/40 border border-trap-purple text-trap-purple"
            >
              <Copy className="w-4 h-4 mr-2" />
              Copy Text
            </Button>
          </div>
          
          <Button
            onClick={handleShare}
            disabled={isSharing}
            className="w-full bg-trap-yellow/20 hover:bg-trap-yellow/40 border border-trap-yellow text-trap-yellow"
          >
            <Share2 className="w-4 h-4 mr-2" />
            {isSharing ? 'Sharing...' : 'Share Satirical Post'}
          </Button>
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 pb-4">
        <div className="text-center text-xs text-gray-500 font-trap">
          🎭 Satirized by Lil LiL da TrapGod Gremlin 🎭
          <br />
          <span className="text-trap-teal">GodsIMiJ Empire • JUUWRAAYYY!</span>
        </div>
      </div>
    </div>
  );
}
