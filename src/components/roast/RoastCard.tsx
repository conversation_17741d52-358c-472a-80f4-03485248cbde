import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  Share2, 
  Download, 
  Flame, 
  Zap, 
  Skull, 
  Trophy,
  ExternalLink,
  Copy
} from 'lucide-react';
import { RoastResult, DevToProfile } from '@/lib/roastEngine';
import { toast } from '@/components/ui/sonner';

interface RoastCardProps {
  roast: RoastResult;
  profile: DevToProfile;
  memeImageUrl?: string;
  className?: string;
}

export default function RoastCard({ roast, profile, memeImageUrl, className }: RoastCardProps) {
  const [isSharing, setIsSharing] = useState(false);

  // Style configurations based on roast intensity
  const intensityStyles = {
    'low-flame': {
      border: 'border-orange-500',
      glow: 'shadow-[0_0_20px_rgba(249,115,22,0.3)]',
      bg: 'bg-gradient-to-br from-orange-500/10 to-yellow-500/10',
      icon: Flame,
      color: 'text-orange-500'
    },
    'mid-flame': {
      border: 'border-red-500',
      glow: 'shadow-[0_0_20px_rgba(239,68,68,0.4)]',
      bg: 'bg-gradient-to-br from-red-500/10 to-pink-500/10',
      icon: Zap,
      color: 'text-red-500'
    },
    'full-juuwraayyy': {
      border: 'border-purple-500',
      glow: 'shadow-[0_0_30px_rgba(147,51,234,0.6)]',
      bg: 'bg-gradient-to-br from-purple-500/20 to-pink-500/20',
      icon: Skull,
      color: 'text-purple-500'
    }
  };

  const styleConfig = intensityStyles[roast.intensity];
  const IntensityIcon = styleConfig.icon;

  const handleShare = async () => {
    setIsSharing(true);
    try {
      if (navigator.share) {
        await navigator.share({
          title: `${profile.username} got roasted by TrapGPT!`,
          text: roast.roast,
          url: window.location.href
        });
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(
          `🔥 ${profile.username} just got roasted by Lil LiL da TrapGod! 🔥\n\n"${roast.roast}"\n\nDamage: ${roast.damage} | Rank: ${roast.rank}\n\nGet roasted at: ${window.location.origin}`
        );
        toast.success('Roast copied to clipboard!');
      }
    } catch (error) {
      console.error('Share failed:', error);
      toast.error('Failed to share roast');
    }
    setIsSharing(false);
  };

  const handleDownload = () => {
    // In a real implementation, this would generate and download the meme image
    toast.success('Meme download started!');
  };

  const handleSendToTarget = () => {
    // Open Dev.to profile in new tab
    window.open(`https://dev.to/${profile.username}`, '_blank');
    toast.success(`Opened ${profile.username}'s Dev.to profile!`);
  };

  const getDamageColor = (damage: number) => {
    if (damage < 2500) return 'text-orange-400';
    if (damage < 6000) return 'text-red-400';
    return 'text-purple-400';
  };

  const getCringeColor = (cringe: number) => {
    if (cringe < 40) return 'text-green-400';
    if (cringe < 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className={cn(
      "w-full max-w-md mx-auto rounded-lg border-2 backdrop-blur-sm",
      "transition-all duration-300 hover:scale-[1.02]",
      styleConfig.border,
      styleConfig.glow,
      styleConfig.bg,
      className
    )}>
      {/* Card Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <IntensityIcon className={cn("w-6 h-6", styleConfig.color)} />
            <h3 className="text-xl font-future text-white">
              ROAST CARD
            </h3>
          </div>
          <div className="flex items-center gap-1">
            <Trophy className="w-4 h-4 text-trap-yellow" />
            <span className="text-sm font-trap text-trap-yellow">
              {roast.rank}
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {profile.profileImage && (
            <img 
              src={profile.profileImage} 
              alt={profile.username}
              className="w-12 h-12 rounded-full border-2 border-trap-teal"
            />
          )}
          <div>
            <h4 className="text-lg font-trap text-trap-teal">
              @{profile.username}
            </h4>
            {profile.name && (
              <p className="text-sm text-gray-400">
                {profile.name}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Meme Image (if available) */}
      {memeImageUrl && (
        <div className="relative">
          <img 
            src={memeImageUrl} 
            alt="Roast Meme"
            className="w-full h-48 object-cover"
          />
          <div className="absolute top-2 right-2">
            <span className="bg-black/80 text-trap-purple text-xs px-2 py-1 rounded font-future">
              🔥 ROASTED BY THE EMPIRE
            </span>
          </div>
        </div>
      )}

      {/* Roast Content */}
      <div className="p-4">
        <div className="mb-4 p-3 bg-black/60 rounded-lg border border-gray-700">
          <p className="text-white font-trap text-lg leading-relaxed">
            "{roast.roast}"
          </p>
        </div>

        {/* Battle Stats */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="text-center p-2 bg-black/40 rounded border border-gray-700">
            <div className={cn("text-2xl font-future", getDamageColor(roast.damage))}>
              {roast.damage.toLocaleString()}
            </div>
            <div className="text-xs text-gray-400 font-trap">
              DAMAGE
            </div>
          </div>
          
          <div className="text-center p-2 bg-black/40 rounded border border-gray-700">
            <div className={cn("text-2xl font-future", getCringeColor(roast.cringeLevel))}>
              {roast.cringeLevel}%
            </div>
            <div className="text-xs text-gray-400 font-trap">
              CRINGE
            </div>
          </div>
          
          <div className="text-center p-2 bg-black/40 rounded border border-gray-700">
            <div className="text-lg font-future text-trap-purple uppercase">
              {roast.style}
            </div>
            <div className="text-xs text-gray-400 font-trap">
              STYLE
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={handleSendToTarget}
              className="bg-trap-purple/20 hover:bg-trap-purple/40 border border-trap-purple text-trap-purple"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Send to Target
            </Button>
            
            <Button
              onClick={handleDownload}
              className="bg-trap-teal/20 hover:bg-trap-teal/40 border border-trap-teal text-trap-teal"
            >
              <Download className="w-4 h-4 mr-2" />
              Save Card
            </Button>
          </div>
          
          <Button
            onClick={handleShare}
            disabled={isSharing}
            className="w-full bg-trap-yellow/20 hover:bg-trap-yellow/40 border border-trap-yellow text-trap-yellow"
          >
            <Share2 className="w-4 h-4 mr-2" />
            {isSharing ? 'Sharing...' : 'Share to Dev.to Feed'}
          </Button>
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 pb-4">
        <div className="text-center text-xs text-gray-500 font-trap">
          🎭 Roasted by Lil LiL da TrapGod Gremlin 🎭
          <br />
          <span className="text-trap-teal">GodsIMiJ Empire • JUUWRAAYYY!</span>
        </div>
      </div>
    </div>
  );
}
