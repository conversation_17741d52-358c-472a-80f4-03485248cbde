import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Flame, 
  Zap, 
  Skull, 
  Target, 
  Loader2,
  AlertTriangle
} from 'lucide-react';
import { generateDevToRoast, fetchDevToProfile, RoastIntensity, RoastResult, DevToProfile } from '@/lib/roastEngine';
import RoastCard from './RoastCard';
import { toast } from '@/components/ui/sonner';

export default function RoastGenerator() {
  const [targetInput, setTargetInput] = useState('');
  const [selectedIntensity, setSelectedIntensity] = useState<RoastIntensity>('mid-flame');
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentRoast, setCurrentRoast] = useState<RoastResult | null>(null);
  const [currentProfile, setCurrentProfile] = useState<DevToProfile | null>(null);
  const [error, setError] = useState<string | null>(null);

  const intensityOptions = [
    {
      id: 'low-flame' as RoastIntensity,
      name: 'Low Flame',
      description: 'Gentle sizzle, friendly fire',
      icon: Flame,
      color: 'text-orange-500',
      bg: 'bg-orange-500/10 hover:bg-orange-500/20',
      border: 'border-orange-500/30 hover:border-orange-500'
    },
    {
      id: 'mid-flame' as RoastIntensity,
      name: 'Mid Flame',
      description: 'Digital BBQ, solid roast',
      icon: Zap,
      color: 'text-red-500',
      bg: 'bg-red-500/10 hover:bg-red-500/20',
      border: 'border-red-500/30 hover:border-red-500'
    },
    {
      id: 'full-juuwraayyy' as RoastIntensity,
      name: 'Full JUUWRAAYYY',
      description: 'Atomic Gremlin, total annihilation',
      icon: Skull,
      color: 'text-purple-500',
      bg: 'bg-purple-500/10 hover:bg-purple-500/20',
      border: 'border-purple-500/30 hover:border-purple-500'
    }
  ];

  const handleGenerateRoast = async () => {
    if (!targetInput.trim()) {
      toast.error('Enter a Dev.to username or profile URL!');
      return;
    }

    setIsGenerating(true);
    setError(null);
    
    try {
      console.log('🎯 Targeting developer:', targetInput);
      
      // Fetch profile data
      const profile = await fetchDevToProfile(targetInput);
      if (!profile) {
        throw new Error('Could not fetch developer profile');
      }
      
      console.log('👤 Profile acquired:', profile);
      
      // Generate roast
      const roast = await generateDevToRoast(profile, selectedIntensity);
      
      console.log('🔥 Roast generated:', roast);
      
      setCurrentProfile(profile);
      setCurrentRoast(roast);
      
      toast.success(`🔥 ${profile.username} has been roasted!`);
      
    } catch (error) {
      console.error('Roast generation failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate roast');
      toast.error('Roast generation failed!');
    }
    
    setIsGenerating(false);
  };

  const handleNewRoast = () => {
    setCurrentRoast(null);
    setCurrentProfile(null);
    setTargetInput('');
    setError(null);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-future text-trap-purple mb-4">
          🎭 DEV.TO ROAST GENERATOR 🎭
        </h1>
        <p className="text-xl font-trap text-gray-300 mb-2">
          Powered by Lil LiL da TrapGod Gremlin
        </p>
        <p className="text-sm text-trap-teal">
          🔥 FLAMEBYTE: THE ROAST CODEX INITIATIVE 🔥
        </p>
      </div>

      {!currentRoast ? (
        <div className="space-y-6">
          {/* Target Input */}
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm">
            <h3 className="text-xl font-future text-trap-teal mb-4 flex items-center gap-2">
              <Target className="w-5 h-5" />
              Target Acquisition
            </h3>
            
            <div className="space-y-4">
              <Input
                value={targetInput}
                onChange={(e) => setTargetInput(e.target.value)}
                placeholder="Enter Dev.to username or profile URL (e.g., @username or dev.to/username)"
                className="bg-black/60 border-gray-700 text-white placeholder-gray-400 text-lg p-4"
                onKeyDown={(e) => e.key === 'Enter' && handleGenerateRoast()}
              />
              
              <p className="text-sm text-gray-400 font-trap">
                💡 Tip: Enter any Dev.to username to target them for a roast!
              </p>
            </div>
          </div>

          {/* Intensity Selector */}
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm">
            <h3 className="text-xl font-future text-trap-teal mb-4">
              🔥 Roast Intensity Level
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {intensityOptions.map((option) => {
                const Icon = option.icon;
                const isSelected = selectedIntensity === option.id;
                
                return (
                  <Button
                    key={option.id}
                    onClick={() => setSelectedIntensity(option.id)}
                    className={cn(
                      "p-4 h-auto flex flex-col items-center gap-3 text-left transition-all duration-300",
                      "border-2 backdrop-blur-sm",
                      option.bg,
                      isSelected ? option.border : 'border-gray-700',
                      isSelected ? 'scale-105' : 'hover:scale-102'
                    )}
                  >
                    <Icon className={cn("w-8 h-8", option.color)} />
                    <div className="text-center">
                      <div className={cn("font-future text-lg", option.color)}>
                        {option.name}
                      </div>
                      <div className="text-sm text-gray-400 font-trap">
                        {option.description}
                      </div>
                    </div>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Generate Button */}
          <div className="text-center">
            <Button
              onClick={handleGenerateRoast}
              disabled={isGenerating || !targetInput.trim()}
              className={cn(
                "px-8 py-4 text-xl font-future",
                "bg-gradient-to-r from-trap-purple to-trap-pink",
                "hover:from-trap-pink hover:to-trap-purple",
                "border-2 border-trap-purple",
                "transition-all duration-300 hover:scale-105",
                "shadow-[0_0_20px_rgba(155,135,245,0.4)]",
                "hover:shadow-[0_0_30px_rgba(155,135,245,0.6)]"
              )}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-6 h-6 mr-2 animate-spin" />
                  Generating Roast...
                </>
              ) : (
                <>
                  <Skull className="w-6 h-6 mr-2" />
                  INITIATE ROAST PROTOCOL
                </>
              )}
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center gap-2 text-red-400">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-future">Roast Generation Failed</span>
              </div>
              <p className="text-red-300 font-trap mt-2">{error}</p>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {/* Generated Roast Card */}
          {currentProfile && (
            <RoastCard 
              roast={currentRoast} 
              profile={currentProfile}
              className="mx-auto"
            />
          )}
          
          {/* New Roast Button */}
          <div className="text-center">
            <Button
              onClick={handleNewRoast}
              className="px-6 py-3 font-future bg-trap-teal/20 hover:bg-trap-teal/40 border border-trap-teal text-trap-teal"
            >
              🎯 Generate Another Roast
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
