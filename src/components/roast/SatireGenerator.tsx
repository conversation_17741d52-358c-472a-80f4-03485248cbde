import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Flame,
  Zap,
  Skull,
  Lightbulb,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import { generateSatiricalPost, SatireIntensity, SatireResult } from '@/lib/satireEngine';
import SatireCard from './SatireCard';
import { toast } from '@/components/ui/sonner';

export default function SatireGenerator() {
  const [topicInput, setTopicInput] = useState('');
  const [selectedIntensity, setSelectedIntensity] = useState<SatireIntensity>('peak-gpt');
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentSatire, setCurrentSatire] = useState<SatireResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const intensityOptions = [
    {
      id: 'mildly-robotic' as SatireIntensity,
      name: 'Mildly Robotic',
      description: 'Basic bot vibes, gentle mockery',
      icon: Flame,
      color: 'text-orange-500',
      bg: 'bg-orange-500/10 hover:bg-orange-500/20',
      border: 'border-orange-500/30 hover:border-orange-500'
    },
    {
      id: 'peak-gpt' as SatireIntensity,
      name: 'Peak GPT',
      description: 'Maximum automation, buzzword slaying',
      icon: Zap,
      color: 'text-red-500',
      bg: 'bg-red-500/10 hover:bg-red-500/20',
      border: 'border-red-500/30 hover:border-red-500'
    },
    {
      id: 'ultra-gptcore' as SatireIntensity,
      name: 'Ultra GPTcore+',
      description: 'Transcendent AI cringe, satirical annihilation',
      icon: Skull,
      color: 'text-purple-500',
      bg: 'bg-purple-500/10 hover:bg-purple-500/20',
      border: 'border-purple-500/30 hover:border-purple-500'
    }
  ];

  const handleGenerateSatire = async () => {
    if (!topicInput.trim()) {
      toast.error('Enter a topic to satirize!');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      console.log('🎭 Satirizing topic:', topicInput);

      // Generate satirical post
      const satire = await generateSatiricalPost(topicInput, selectedIntensity);

      console.log('🔥 Satirical post generated:', satire);

      setCurrentSatire(satire);

      toast.success(`🎭 Satirical post generated with ${satire.gptLevel} energy!`);

    } catch (error) {
      console.error('Satirical post generation failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate satirical post');
      toast.error('Satirical post generation failed!');
    }

    setIsGenerating(false);
  };

  const handleNewSatire = () => {
    setCurrentSatire(null);
    setTopicInput('');
    setError(null);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-future text-trap-purple mb-4">
          🎭 SATIRICAL POST GENERATOR 🎭
        </h1>
        <p className="text-xl font-trap text-gray-300 mb-2">
          Powered by Lil LiL da TrapGod Gremlin
        </p>
        <p className="text-sm text-trap-teal">
          🔥 FLAMEBYTE: THE ROAST CODEX INITIATIVE 🔥
        </p>
      </div>

      {!currentSatire ? (
        <div className="space-y-6">
          {/* Topic Input */}
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm">
            <h3 className="text-xl font-future text-trap-teal mb-4 flex items-center gap-2">
              <Lightbulb className="w-5 h-5" />
              Topic Selection
            </h3>

            <div className="space-y-4">
              <Input
                value={topicInput}
                onChange={(e) => setTopicInput(e.target.value)}
                placeholder="e.g. 'Why I Built a To-Do App with Blockchain'"
                className="bg-black/60 border-gray-700 text-white placeholder-gray-400 text-lg p-4"
                onKeyDown={(e) => e.key === 'Enter' && handleGenerateSatire()}
              />

              <p className="text-sm text-gray-400 font-trap">
                💡 Tip: Enter any tech topic to generate a satirical blog post parody!
              </p>
            </div>
          </div>

          {/* Intensity Selector */}
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm">
            <h3 className="text-xl font-future text-trap-teal mb-4">
              🤖 GPT Satirical Level
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {intensityOptions.map((option) => {
                const Icon = option.icon;
                const isSelected = selectedIntensity === option.id;

                return (
                  <Button
                    key={option.id}
                    onClick={() => setSelectedIntensity(option.id)}
                    className={cn(
                      "p-4 h-auto flex flex-col items-center gap-3 text-left transition-all duration-300",
                      "border-2 backdrop-blur-sm",
                      option.bg,
                      isSelected ? option.border : 'border-gray-700',
                      isSelected ? 'scale-105' : 'hover:scale-102'
                    )}
                  >
                    <Icon className={cn("w-8 h-8", option.color)} />
                    <div className="text-center">
                      <div className={cn("font-future text-lg", option.color)}>
                        {option.name}
                      </div>
                      <div className="text-sm text-gray-400 font-trap">
                        {option.description}
                      </div>
                    </div>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Generate Button */}
          <div className="text-center">
            <Button
              onClick={handleGenerateSatire}
              disabled={isGenerating || !topicInput.trim()}
              className={cn(
                "px-8 py-4 text-xl font-future",
                "bg-gradient-to-r from-trap-purple to-trap-pink",
                "hover:from-trap-pink hover:to-trap-purple",
                "border-2 border-trap-purple",
                "transition-all duration-300 hover:scale-105",
                "shadow-[0_0_20px_rgba(155,135,245,0.4)]",
                "hover:shadow-[0_0_30px_rgba(155,135,245,0.6)]"
              )}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-6 h-6 mr-2 animate-spin" />
                  Generating Satirical Post...
                </>
              ) : (
                <>
                  <Skull className="w-6 h-6 mr-2" />
                  GENERATE SATIRICAL POST
                </>
              )}
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center gap-2 text-red-400">
                <AlertTriangle className="w-5 h-5" />
                <span className="font-future">Satirical Post Generation Failed</span>
              </div>
              <p className="text-red-300 font-trap mt-2">{error}</p>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {/* Generated Satirical Post Card */}
          <SatireCard
            satire={currentSatire}
            className="mx-auto"
          />

          {/* New Satirical Post Button */}
          <div className="text-center">
            <Button
              onClick={handleNewSatire}
              className="px-6 py-3 font-future bg-trap-teal/20 hover:bg-trap-teal/40 border border-trap-teal text-trap-teal"
            >
              🎭 Generate Another Satirical Post
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
