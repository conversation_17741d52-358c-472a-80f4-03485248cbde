import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  Pause, 
  Radio,
  ChevronUp,
  ChevronDown,
  Music,
  Headphones,
  Mic,
  SkipForward,
  SkipBack
} from 'lucide-react';
import { useRadio } from '@/contexts/RadioContext';
import { useChatMode } from '@/contexts/ChatModeContext';

interface RadioWidgetProps {
  className?: string;
}

export default function RadioWidget({ className }: RadioWidgetProps) {
  const { mode } = useChatMode();
  const { 
    isPlaying, 
    currentTrack, 
    listeners, 
    togglePlay, 
    nextTrack, 
    previousTrack 
  } = useRadio();
  
  const [isExpanded, setIsExpanded] = useState(false);

  const modeStyles = {
    OG: "border-trap-green shadow-[0_0_10px_rgba(57,255,20,0.2)]",
    GODMODE: "border-trap-yellow shadow-[0_0_10px_rgba(255,255,0,0.2)]",
    ROAST: "border-red-500 shadow-[0_0_10px_rgba(255,0,0,0.2)]",
    STEP: "border-blue-500 shadow-[0_0_10px_rgba(0,149,255,0.2)]",
    RAW: "border-trap-purple shadow-[0_0_10px_rgba(155,135,245,0.2)]",
  };

  const trackTypeIcons = {
    empire: Music,
    stream: Headphones,
    interlude: Mic
  };

  const trackTypeColors = {
    empire: "text-trap-yellow",
    stream: "text-trap-teal", 
    interlude: "text-trap-purple"
  };

  if (!currentTrack) return null;

  const TrackIcon = trackTypeIcons[currentTrack.type];

  return (
    <div className={cn(
      "fixed bottom-4 right-4 z-50",
      "bg-black/90 backdrop-blur-sm border rounded-lg",
      "transition-all duration-300",
      mode && modeStyles[mode],
      isExpanded ? "w-80" : "w-64",
      className
    )}>
      {/* Compact Header */}
      <div className="flex items-center gap-3 p-3">
        <Button
          onClick={togglePlay}
          size="sm"
          className="p-2 rounded-full bg-trap-purple/20 hover:bg-trap-purple/40 flex-shrink-0"
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
        </Button>
        
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Radio className="w-4 h-4 text-trap-teal animate-pulse flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <div className="text-sm font-trap text-white truncate">
              {currentTrack.title}
            </div>
            <div className="text-xs text-gray-400 truncate">
              {currentTrack.artist}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2 flex-shrink-0">
          <div className="flex items-center gap-1 text-xs text-gray-400">
            <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
            LIVE
          </div>
          
          <Button
            onClick={() => setIsExpanded(!isExpanded)}
            variant="ghost"
            size="sm"
            className="p-1"
          >
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronUp className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="px-3 pb-3 border-t border-gray-700">
          {/* Track Info */}
          <div className="py-3">
            <div className="flex items-center gap-2 mb-2">
              <TrackIcon className={cn("w-4 h-4", trackTypeColors[currentTrack.type])} />
              <span className={cn(
                "text-xs px-2 py-1 rounded-full border",
                trackTypeColors[currentTrack.type],
                "border-current"
              )}>
                {currentTrack.type.toUpperCase()}
              </span>
              {currentTrack.duration && (
                <span className="text-xs text-gray-500">
                  {currentTrack.duration}
                </span>
              )}
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between mb-3">
            <Button
              onClick={previousTrack}
              variant="ghost"
              size="sm"
              className="p-2"
            >
              <SkipBack className="w-4 h-4" />
            </Button>

            <div className="text-center">
              <div className="text-xs text-trap-teal font-future">
                JUUWRAAYY RADIO
              </div>
              <div className="text-xs text-gray-400">
                {listeners.toLocaleString()} listeners
              </div>
            </div>

            <Button
              onClick={nextTrack}
              variant="ghost"
              size="sm"
              className="p-2"
            >
              <SkipForward className="w-4 h-4" />
            </Button>
          </div>

          {/* Status */}
          <div className="text-center">
            <p className="text-xs text-gray-400 font-trap">
              🎵 GodsIMiJ Empire 24/7 🎵
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
