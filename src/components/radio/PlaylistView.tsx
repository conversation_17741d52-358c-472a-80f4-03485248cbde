import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Music,
  Headphones,
  Mic,
  Play,
  Clock
} from 'lucide-react';
import { useRadio } from '@/contexts/RadioContext';
import { useChatMode } from '@/contexts/ChatModeContext';

interface PlaylistViewProps {
  className?: string;
}

export default function PlaylistView({ className }: PlaylistViewProps) {
  const { mode } = useChatMode();
  const { playlist, currentTrack, setCurrentTrack } = useRadio();

  const modeStyles = {
    OG: "border-trap-green",
    GODMODE: "border-trap-yellow",
    ROAST: "border-red-500",
    STEP: "border-blue-500",
    RAW: "border-trap-purple",
  };

  const trackTypeIcons = {
    empire: Music,
    stream: Headphones,
    interlude: Mic
  };

  const trackTypeColors = {
    empire: "text-trap-yellow bg-trap-yellow/10 border-trap-yellow/30",
    stream: "text-trap-teal bg-trap-teal/10 border-trap-teal/30", 
    interlude: "text-trap-purple bg-trap-purple/10 border-trap-purple/30"
  };

  const trackTypeLabels = {
    empire: "EMPIRE TRACK",
    stream: "STREAMED SET",
    interlude: "LIL LIL INTERLUDE"
  };

  return (
    <div className={cn(
      "w-full max-w-2xl p-6 rounded-lg",
      "bg-black/40 backdrop-blur-sm border-2",
      mode && modeStyles[mode],
      "transition-all duration-300",
      className
    )}>
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <Music className="w-6 h-6 text-trap-teal" />
        <h3 className="text-2xl font-future text-trap-teal">
          JUUWRAAYY RADIO PLAYLIST
        </h3>
      </div>

      {/* Playlist */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {playlist.map((track, index) => {
          const TrackIcon = trackTypeIcons[track.type];
          const isCurrentTrack = currentTrack?.id === track.id;
          
          return (
            <div
              key={track.id}
              className={cn(
                "p-4 rounded-lg border transition-all duration-300",
                "hover:scale-[1.02] cursor-pointer",
                isCurrentTrack 
                  ? "bg-trap-purple/20 border-trap-purple shadow-[0_0_15px_rgba(155,135,245,0.3)]"
                  : "bg-black/60 border-gray-700 hover:border-gray-600"
              )}
              onClick={() => setCurrentTrack(track)}
            >
              <div className="flex items-center gap-4">
                {/* Track Number */}
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-sm font-future",
                  isCurrentTrack 
                    ? "bg-trap-purple text-white" 
                    : "bg-gray-700 text-gray-400"
                )}>
                  {isCurrentTrack ? (
                    <Play className="w-4 h-4" />
                  ) : (
                    String(index + 1).padStart(2, '0')
                  )}
                </div>

                {/* Track Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <TrackIcon className={cn("w-4 h-4", trackTypeColors[track.type].split(' ')[0])} />
                    <span className={cn(
                      "text-xs px-2 py-1 rounded-full border font-future",
                      trackTypeColors[track.type]
                    )}>
                      {trackTypeLabels[track.type]}
                    </span>
                  </div>
                  
                  <h4 className={cn(
                    "font-trap text-lg truncate",
                    isCurrentTrack ? "text-trap-purple" : "text-white"
                  )}>
                    {track.title}
                  </h4>
                  
                  <p className="text-sm text-gray-400 truncate">
                    {track.artist}
                  </p>
                </div>

                {/* Duration */}
                {track.duration && (
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Clock className="w-4 h-4" />
                    {track.duration}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-gray-700 text-center">
        <p className="text-sm text-gray-400 font-trap">
          🎵 {playlist.length} tracks • GodsIMiJ Empire Collection 🎵
        </p>
        <p className="text-xs text-trap-teal mt-1">
          Curated by Lil LiL da TrapGod Gremlin
        </p>
      </div>
    </div>
  );
}
