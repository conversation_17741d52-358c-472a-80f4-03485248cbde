import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Radio,
  Headphones,
  Music,
  Mic
} from 'lucide-react';
import { useChatMode } from '@/contexts/ChatModeContext';

interface Track {
  title: string;
  artist: string;
  type: 'empire' | 'stream' | 'interlude';
  duration?: string;
}

interface RadioPlayerProps {
  className?: string;
  compact?: boolean;
}

export default function RadioPlayer({ className, compact = false }: RadioPlayerProps) {
  const { mode } = useChatMode();
  const audioRef = useRef<HTMLAudioElement>(null);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<Track>({
    title: "JUUWRAAYY RADIO",
    artist: "<PERSON> LiL da TrapGod",
    type: 'interlude'
  });
  const [isLive, setIsLive] = useState(true);
  const [listeners, setListeners] = useState(1337);

  // Sample playlist - in production this would come from an API
  const playlist: Track[] = [
    { title: "JUUWRAAYY RADIO", artist: "Lil LiL da TrapGod", type: 'interlude' },
    { title: "Empire Anthem", artist: "GodsIMiJ", type: 'empire', duration: "3:45" },
    { title: "Gremlin Flow", artist: "Lil LiL", type: 'stream', duration: "4:12" },
    { title: "TrapGod Chronicles", artist: "GodsIMiJ Empire", type: 'empire', duration: "5:23" },
    { title: "Live from the Glitchspace", artist: "Lil LiL", type: 'interlude' },
  ];

  // Mode-specific styling
  const modeStyles = {
    OG: "border-trap-green shadow-[0_0_15px_rgba(57,255,20,0.3)]",
    GODMODE: "border-trap-yellow shadow-[0_0_15px_rgba(255,255,0,0.3)]",
    ROAST: "border-red-500 shadow-[0_0_15px_rgba(255,0,0,0.3)]",
    STEP: "border-blue-500 shadow-[0_0_15px_rgba(0,149,255,0.3)]",
    RAW: "border-trap-purple shadow-[0_0_15px_rgba(155,135,245,0.3)]",
  };

  const trackTypeIcons = {
    empire: Music,
    stream: Headphones,
    interlude: Mic
  };

  const trackTypeColors = {
    empire: "text-trap-yellow",
    stream: "text-trap-teal", 
    interlude: "text-trap-purple"
  };

  useEffect(() => {
    // Simulate track changes every 30 seconds
    const interval = setInterval(() => {
      const randomTrack = playlist[Math.floor(Math.random() * playlist.length)];
      setCurrentTrack(randomTrack);
      
      // Simulate listener count changes
      setListeners(prev => prev + Math.floor(Math.random() * 10) - 5);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        // In a real implementation, this would connect to a live stream
        audioRef.current.play().catch(console.error);
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (audioRef.current) {
      audioRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };

  const TrackIcon = trackTypeIcons[currentTrack.type];

  if (compact) {
    return (
      <div className={cn(
        "flex items-center gap-3 p-3 rounded-lg",
        "bg-black/40 backdrop-blur-sm border",
        mode && modeStyles[mode],
        className
      )}>
        <Button
          onClick={togglePlay}
          size="sm"
          className="p-2 rounded-full bg-trap-purple/20 hover:bg-trap-purple/40"
        >
          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
        </Button>
        
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Radio className="w-4 h-4 text-trap-teal animate-pulse" />
          <span className="text-sm font-trap text-white truncate">
            {currentTrack.title}
          </span>
        </div>
        
        <div className="flex items-center gap-1 text-xs text-gray-400">
          <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
          LIVE
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "w-full max-w-md p-6 rounded-lg",
      "bg-black/40 backdrop-blur-sm border-2",
      mode && modeStyles[mode],
      "transition-all duration-300 hover:scale-[1.02]",
      className
    )}>
      {/* Hidden audio element for future streaming */}
      <audio ref={audioRef} preload="none" />
      
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Radio className="w-6 h-6 text-trap-teal animate-pulse" />
          <h3 className="text-xl font-future text-trap-teal">
            JUUWRAAYY RADIO
          </h3>
        </div>
        
        <div className="flex items-center gap-2 text-sm">
          <span className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
          <span className="text-red-400 font-trap">LIVE</span>
          <span className="text-gray-400">•</span>
          <span className="text-gray-400">{listeners.toLocaleString()}</span>
        </div>
      </div>

      {/* Current Track Info */}
      <div className="mb-6 p-4 bg-black/60 rounded-lg border border-gray-700">
        <div className="flex items-center gap-3 mb-2">
          <TrackIcon className={cn("w-5 h-5", trackTypeColors[currentTrack.type])} />
          <span className={cn(
            "text-xs px-2 py-1 rounded-full border",
            trackTypeColors[currentTrack.type],
            "border-current"
          )}>
            {currentTrack.type.toUpperCase()}
          </span>
        </div>
        
        <h4 className="text-lg font-trap text-white mb-1">
          {currentTrack.title}
        </h4>
        <p className="text-sm text-gray-400">
          {currentTrack.artist}
        </p>
        
        {currentTrack.duration && (
          <p className="text-xs text-gray-500 mt-1">
            Duration: {currentTrack.duration}
          </p>
        )}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between mb-4">
        <Button
          onClick={togglePlay}
          className={cn(
            "p-4 rounded-full transition-all duration-300",
            "bg-trap-purple/20 hover:bg-trap-purple/40",
            "border-2 border-trap-purple",
            "hover:scale-110"
          )}
        >
          {isPlaying ? (
            <Pause className="w-6 h-6 text-trap-purple" />
          ) : (
            <Play className="w-6 h-6 text-trap-purple" />
          )}
        </Button>

        <div className="flex items-center gap-3 flex-1 ml-4">
          <Button
            onClick={toggleMute}
            variant="ghost"
            size="sm"
            className="p-2"
          >
            {isMuted ? (
              <VolumeX className="w-5 h-5 text-gray-400" />
            ) : (
              <Volume2 className="w-5 h-5 text-gray-400" />
            )}
          </Button>
          
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={handleVolumeChange}
            className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
        </div>
      </div>

      {/* Status */}
      <div className="text-center">
        <p className="text-xs text-gray-400 font-trap">
          🎵 Bumping GodsIMiJ Empire tracks 24/7 🎵
        </p>
        <p className="text-xs text-trap-teal mt-1">
          Hosted by Lil LiL da TrapGod Gremlin
        </p>
      </div>
    </div>
  );
}
