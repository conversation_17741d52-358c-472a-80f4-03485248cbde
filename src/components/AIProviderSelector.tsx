import { useState, useEffect } from "react";
import { useChatMode } from "@/contexts/ChatModeContext";
import { cn } from "@/lib/utils";
import { Button } from "./ui/button";
import { Zap, Server, Cpu, Wifi, WifiOff } from "lucide-react";
import { AIProvider } from "@/lib/gremlinRouter";

interface AIProviderSelectorProps {
  className?: string;
}

export default function AIProviderSelector({ className }: AIProviderSelectorProps) {
  const { aiProvider, setAiProvider, setIsApiConnected } = useChatMode();
  const [providers, setProviders] = useState({
    openai: false,
    local: false
  });
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Simplified initialization - just set defaults for now
    setIsChecking(false);
    setProviders({ openai: true, local: false }); // Assume OpenAI available
    setAiProvider('openai');
    setIsApiConnected(true);
  }, []);

  const checkProviders = async () => {
    // Simplified for now - we'll enhance this later
    setIsChecking(true);
    setTimeout(() => {
      setProviders({ openai: true, local: false });
      setIsChecking(false);
    }, 1000);
  };

  const handleProviderSelect = (provider: AIProvider) => {
    setAiProvider(provider);

    // Update connection status
    const isConnected =
      provider === 'openai' ? providers.openai :
      provider === 'local' ? providers.local :
      false;

    setIsApiConnected(isConnected);
  };

  const providerConfig = {
    openai: {
      name: "OpenAI GPT-4o",
      icon: Zap,
      description: "Cloud AI with maximum power",
      available: providers.openai,
      color: "text-trap-yellow"
    },
    local: {
      name: "Local Model",
      icon: Server,
      description: "LM Studio/Ollama on localhost",
      available: providers.local,
      color: "text-trap-teal"
    },
    fallback: {
      name: "Gremlin Core",
      icon: Cpu,
      description: "Built-in responses (always available)",
      available: true,
      color: "text-trap-purple"
    }
  };

  return (
    <div className={cn("w-full max-w-md", className)}>
      <div className="mb-4 text-center">
        <h3 className="text-lg font-future text-trap-teal mb-2">
          AI POWER SOURCE
        </h3>
        <div className="flex items-center justify-center gap-2 mb-3">
          {isChecking ? (
            <WifiOff className="w-4 h-4 text-gray-500 animate-pulse" />
          ) : (
            <Wifi className={cn(
              "w-4 h-4",
              providers.openai || providers.local ? "text-trap-green" : "text-red-500"
            )} />
          )}
          <span className="text-sm font-trap text-gray-400">
            {isChecking ? "Scanning..." :
             providers.openai || providers.local ? "Connected" : "Offline Mode"}
          </span>
        </div>
      </div>

      <div className="space-y-3">
        {(Object.keys(providerConfig) as AIProvider[]).map((provider) => {
          const config = providerConfig[provider];
          const Icon = config.icon;
          const isSelected = aiProvider === provider;
          const isAvailable = config.available;

          return (
            <Button
              key={provider}
              onClick={() => handleProviderSelect(provider)}
              disabled={!isAvailable || isChecking}
              className={cn(
                "w-full p-4 h-auto flex items-center gap-3 text-left",
                "bg-background/20 backdrop-blur-sm border transition-all duration-300",
                isSelected ? [
                  "border-current",
                  config.color,
                  "bg-black/50",
                  "shadow-[0_0_15px_rgba(155,135,245,0.3)]"
                ] : "border-muted/30 hover:border-muted/50",
                !isAvailable && "opacity-50 cursor-not-allowed"
              )}
            >
              <Icon className={cn(
                "w-6 h-6 flex-shrink-0",
                isSelected ? config.color : "text-gray-400"
              )} />

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className={cn(
                    "font-future text-sm",
                    isSelected ? config.color : "text-white"
                  )}>
                    {config.name}
                  </span>

                  {isAvailable ? (
                    <span className="text-xs border border-trap-green text-trap-green px-2 py-1 rounded">
                      READY
                    </span>
                  ) : (
                    <span className="text-xs border border-red-500 text-red-500 px-2 py-1 rounded">
                      OFFLINE
                    </span>
                  )}
                </div>

                <p className="text-xs text-gray-400 font-trap">
                  {config.description}
                </p>
              </div>
            </Button>
          );
        })}
      </div>

      <div className="mt-4 text-center">
        <Button
          onClick={checkProviders}
          disabled={isChecking}
          className="text-xs font-future bg-background/20 border border-muted/30 hover:border-trap-teal"
        >
          {isChecking ? "Scanning..." : "Refresh Connections"}
        </Button>
      </div>

      {aiProvider === 'fallback' && (
        <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-md">
          <p className="text-xs text-yellow-400 font-trap text-center">
            Running in offline mode. Add OpenAI API key or start local model for enhanced responses.
          </p>
        </div>
      )}
    </div>
  );
}
