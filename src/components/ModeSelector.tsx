
import { useState } from "react";
import { cn } from "@/lib/utils";
import { useChatMode, ChatMode } from "@/contexts/ChatModeContext";
import { Button } from "./ui/button";
import GlitchText from "./GlitchText";

const modes: ChatMode[] = ["OG", "GODMODE", "ROAST", "STEP", "RAW"];

const modeStyles: Record<ChatMode, { shadow: string; text: string }> = {
  OG: {
    shadow: "shadow-[0_0_15px_rgba(57,255,20,0.3)] hover:shadow-[0_0_25px_rgba(57,255,20,0.5)]",
    text: "text-trap-green"
  },
  GODMODE: {
    shadow: "shadow-[0_0_15px_rgba(255,255,0,0.3)] hover:shadow-[0_0_25px_rgba(255,255,0,0.5)]",
    text: "text-trap-yellow"
  },
  ROAST: {
    shadow: "shadow-[0_0_15px_rgba(255,0,0,0.3)] hover:shadow-[0_0_25px_rgba(255,0,0,0.5)]",
    text: "text-red-500"
  },
  STEP: {
    shadow: "shadow-[0_0_15px_rgba(0,149,255,0.3)] hover:shadow-[0_0_25px_rgba(0,149,255,0.5)]",
    text: "text-blue-500"
  },
  RAW: {
    shadow: "shadow-[0_0_15px_rgba(155,135,245,0.3)] hover:shadow-[0_0_25px_rgba(155,135,245,0.5)]",
    text: "text-trap-purple"
  }
};

interface ModeSelectorProps {
  onSelectComplete?: () => void;
}

export default function ModeSelector({ onSelectComplete }: ModeSelectorProps) {
  const { mode, setMode } = useChatMode();
  const [isSelecting, setIsSelecting] = useState(true);

  const handleModeSelect = (selectedMode: ChatMode) => {
    setMode(selectedMode);
    setIsSelecting(false);

    setTimeout(() => {
      if (onSelectComplete) onSelectComplete();
    }, 1000);
  };

  return (
    <div className={cn(
      "w-full max-w-xs mb-12 text-center transition-opacity duration-1000",
      isSelecting ? "opacity-100" : "opacity-0"
    )}>
      <GlitchText
        text="SELECT YO VIBE"
        className="text-2xl mb-4 tracking-wider"
      />

      <div className="flex flex-col gap-2">
        {modes.map((modeOption) => (
          <Button
            key={modeOption}
            onClick={() => handleModeSelect(modeOption)}
            className={cn(
              "font-future relative overflow-hidden text-white",
              "bg-background/20 backdrop-blur-sm border border-trap-purple/30",
              "transition-all duration-300 hover:scale-105",
              modeStyles[modeOption].shadow,
              "after:content-[''] after:absolute after:inset-0",
              "after:bg-gradient-to-r after:from-trap-purple/10 after:to-trap-teal/10",
              "after:opacity-0 hover:after:opacity-100 after:transition-opacity",
              modeOption === mode ? [
                "border-current",
                modeStyles[modeOption].text,
                "bg-black/50",
                "box-glow"
              ] : "hover:text-white"
            )}
          >
            {modeOption}
          </Button>
        ))}
      </div>

      {mode && (
        <p className={cn(
          "mt-4 animate-pulse font-trap",
          modeStyles[mode].text
        )}>
          {mode} MODE ACTIVATED
        </p>
      )}
    </div>
  );
}
