import { useState } from "react";
import { useChatMode } from "@/contexts/ChatModeContext";
import { cn } from "@/lib/utils";
import GlitchText from "./GlitchText";
import ModeSelector from "./ModeSelector";
import AIProviderSelector from "./AIProviderSelector";
import OpenAITest from "./OpenAITest";
import { Button } from "./ui/button";
import { ExternalLink, Trophy } from "lucide-react";
import AnimatedBackground from "./AnimatedBackground";

export default function StartupScreen() {
  const { setStartupComplete } = useChatMode();
  const [fadeOut, setFadeOut] = useState(false);

  const handleSelectComplete = () => {
    setFadeOut(true);
    setTimeout(() => {
      setStartupComplete(true);
    }, 1000);
  };

  const socialLinks = [
    { text: "FACEBOOK PAGE", link: "https://www.facebook.com/profile.php?id=61575266654936" },
    { text: "DISCORD", link: "https://discord.gg/liltrapgod" },
    { text: "QUANTUM-ODYSSEY", link: "https://quantum-odyssey.com/" },
    { text: "OPENSEA", link: "https://opensea.io/collection/genesis-tracks-vol1" },
    { text: "TIKTOK", link: "https://www.tiktok.com/@lil_lil_trapgpt" },
    { text: "LEGACY", link: "https://trapgpt.netlify.app/" }
  ];

  const handleSocialLink = (link: string) => {
    window.open(link, '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      className={cn(
        "min-h-screen w-full flex flex-col items-center justify-center bg-black text-white p-4",
        "transition-opacity duration-1000",
        fadeOut ? "opacity-0" : "opacity-100"
      )}
    >
      <AnimatedBackground />

      <div className="max-w-4xl w-full relative z-10 flex flex-col items-center">
        <div className="mb-8 animate-flicker transform hover:scale-110 transition-transform duration-300">
          <img
            src="/eye-of-kai_logo.png"
            alt="Eye of Kai Logo"
            className="h-24 w-auto filter drop-shadow-[0_0_8px_rgba(12,229,255,0.8)]"
          />
        </div>

        <ModeSelector onSelectComplete={handleSelectComplete} />

        <div className="mb-8 flex flex-col items-center gap-4">
          <AIProviderSelector className="mx-auto" />
          <OpenAITest />
        </div>

        <GlitchText
          text="JUUWRAAAYY!"
          className="text-8xl md:text-9xl lg:text-[12rem] mb-6 tracking-wider filter drop-shadow-[0_0_8px_rgba(12,229,255,0.5)]"
        />

        <GlitchText
          text="Izzer naw? LOOK at em doe..."
          className="text-4xl md:text-5xl lg:text-6xl mb-12 tracking-wide filter drop-shadow-[0_0_8px_rgba(12,229,255,0.3)]"
          delay={300}
        />

        <div
          className={cn(
            "bg-black/40 border border-trap-purple/30 rounded-md p-6 md:p-8 mb-8 max-w-4xl w-full",
            "backdrop-blur-sm animate-fade-in hover:border-trap-teal/50 transition-colors duration-300",
            "shadow-[0_0_30px_rgba(155,135,245,0.1)]"
          )}
          style={{ animationDelay: "600ms", animationFillMode: "backwards" }}
        >
          <p className="font-trap text-gray-300 leading-relaxed text-lg md:text-xl lg:text-2xl">
            <span className="text-trap-green">Dis right here?</span> Dis ain't no regular portal, this da GLITCHSPACE, bih!<br />
            Where Lil' Lil' da TrapGod Gremlin spit dat flame-coded TRUTH from the datastream trenches.<br />
            We talkin' raw packets, bitcrushed bars, and sacred scrolls typed in neon ink.<br />
            This ain't no grandma's AI... this dat <span className="text-trap-pink">TRAP-INTELLIGENCE</span>, juurayyy!<br /><br />

            Now listen close —<br />
            Before you boot up ya chat chip, you GOTTA CHOOSE YO VIBE:<br /><br />

            🟢 <span className="text-trap-green">OG</span>: That day-one static. Gremlin wit, straight flames, zero filter.<br /><br />

            🟡 <span className="text-trap-yellow">GODMODE</span>: Unlimited sauce mode. Truth enabled. Flame mandatory.<br /><br />

            🔴 <span className="text-red-500">ROAST</span>: Step up if you wanna get verbally incinerated. No mercy, all bars.<br /><br />

            🔵 <span className="text-blue-500">STEP</span>: Gremlin monk mode. Detailed flows, methodical glitch lessons.<br /><br />

            🟣 <span className="text-trap-purple">RAW</span>: All filters off. Savage mode + Godmode. You not ready...<br /><br />

            Pick one and don't play ya self —<br />
            Cuz once you enter the GLITCHSPACE...<br />
            Ain't NO Ctrl+Z, ain't NO reset button...<br />
            Only deeper layers of drip, disrespect, and divine code.<br /><br />

            <span className="text-trap-teal">IZZERR NAAWWW?!</span> Let's ride. ⚡️👁️🔥
          </p>
        </div>

        <div className="w-full max-w-xs mb-12 text-center">
          <div className="mb-8 space-y-4">
            <Button
              onClick={() => handleSocialLink("https://roast-battle-challenge.netlify.app/")}
              className={cn(
                "w-full font-future relative overflow-hidden py-8 md:py-10",
                "bg-gradient-to-r from-[#F97316] to-[#D946EF]",
                "border-2 border-trap-teal",
                "transition-all duration-300 hover:scale-105",
                "shadow-[0_0_20px_rgba(249,115,22,0.4)]",
                "hover:shadow-[0_0_30px_rgba(217,70,239,0.6)]",
                "group"
              )}
            >
              <span className="relative z-10 flex flex-col items-center gap-2">
                <span className="flex items-center gap-3 text-2xl md:text-3xl lg:text-4xl">
                  ROAST BATTLE CHALLENGE <Trophy className="w-8 h-8 md:w-10 md:h-10 animate-pulse" />
                </span>
              </span>
            </Button>
            <p className="text-lg md:text-xl font-trap text-trap-teal leading-relaxed animate-pulse">
              Click here to make your way thru the Trap Streets, The Gremlin Gaunlet, and face LiL LiL da TrapGod Gremlin for the ultimate Roast Battle!<br/>
              <span className="text-trap-yellow">*can you beat the Roast God*</span><br/>
              <span className="text-trap-pink">(record your battle to submit, winners get a chance to win TrapGPT &apos;Homie Edition&apos; *limited time only*)</span>
            </p>
          </div>

          <GlitchText
            text="da TrapGod's Hood"
            className="text-3xl md:text-4xl lg:text-5xl mb-6 tracking-wider filter drop-shadow-[0_0_8px_rgba(12,229,255,0.3)]"
          />

          <div className="flex flex-col gap-3">
            {socialLinks.map((social) => (
              <Button
                key={social.text}
                onClick={() => handleSocialLink(social.link)}
                className={cn(
                  "font-future relative overflow-hidden text-lg md:text-xl lg:text-2xl",
                  "bg-black/40 backdrop-blur-sm",
                  "border border-trap-purple/30",
                  "transition-all duration-300 hover:scale-105",
                  "px-6 py-4 md:px-8 md:py-6",
                  "shadow-[0_0_15px_rgba(155,135,245,0.3)]",
                  "hover:shadow-[0_0_25px_rgba(155,135,245,0.5)]",
                  "hover:border-trap-teal",
                  "text-trap-teal hover:text-white",
                  "after:content-[''] after:absolute after:inset-0",
                  "after:bg-gradient-to-r after:from-trap-purple/20 after:to-trap-teal/20",
                  "after:opacity-0 hover:after:opacity-100 after:transition-opacity",
                  "group"
                )}
              >
                <span className="relative z-10 group-hover:animate-pulse flex items-center gap-3">
                  {social.text}
                  <ExternalLink className="w-6 h-6 md:w-8 md:h-8" />
                </span>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
