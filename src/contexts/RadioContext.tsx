import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Track {
  id: string;
  title: string;
  artist: string;
  type: 'empire' | 'stream' | 'interlude';
  duration?: string;
  url?: string;
}

interface RadioContextType {
  isPlaying: boolean;
  setIsPlaying: (playing: boolean) => void;
  currentTrack: Track | null;
  setCurrentTrack: (track: Track) => void;
  volume: number;
  setVolume: (volume: number) => void;
  isMuted: boolean;
  setIsMuted: (muted: boolean) => void;
  isLive: boolean;
  listeners: number;
  playlist: Track[];
  nextTrack: () => void;
  previousTrack: () => void;
  togglePlay: () => void;
  toggleMute: () => void;
}

const RadioContext = createContext<RadioContextType | undefined>(undefined);

// Sample GodsIMiJ Empire playlist
const EMPIRE_PLAYLIST: Track[] = [
  {
    id: '1',
    title: "JUUWRAAYY RADIO INTRO",
    artist: "<PERSON> LiL da TrapGod",
    type: 'interlude',
    duration: "0:30"
  },
  {
    id: '2', 
    title: "Empire Rising",
    artist: "GodsIMiJ",
    type: 'empire',
    duration: "3:45"
  },
  {
    id: '3',
    title: "Gremlin Chronicles",
    artist: "Lil LiL",
    type: 'stream',
    duration: "4:12"
  },
  {
    id: '4',
    title: "TrapGod Anthem",
    artist: "GodsIMiJ Empire",
    type: 'empire',
    duration: "5:23"
  },
  {
    id: '5',
    title: "Live from the Glitchspace",
    artist: "Lil LiL da TrapGod",
    type: 'interlude',
    duration: "1:15"
  },
  {
    id: '6',
    title: "Divine Frequencies",
    artist: "GodsIMiJ",
    type: 'empire',
    duration: "4:33"
  },
  {
    id: '7',
    title: "Trap Wisdom Sessions",
    artist: "Lil LiL",
    type: 'stream',
    duration: "6:18"
  },
  {
    id: '8',
    title: "Empire State of Mind",
    artist: "GodsIMiJ Empire",
    type: 'empire',
    duration: "3:57"
  },
  {
    id: '9',
    title: "Gremlin Interlude: Real Talk",
    artist: "Lil LiL da TrapGod",
    type: 'interlude',
    duration: "2:03"
  },
  {
    id: '10',
    title: "Infinite Trap Loop",
    artist: "GodsIMiJ",
    type: 'empire',
    duration: "7:42"
  }
];

interface RadioProviderProps {
  children: ReactNode;
}

export function RadioProvider({ children }: RadioProviderProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTrack, setCurrentTrack] = useState<Track | null>(EMPIRE_PLAYLIST[0]);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [isLive] = useState(true);
  const [listeners, setListeners] = useState(1337);
  const [currentTrackIndex, setCurrentTrackIndex] = useState(0);

  // Simulate listener count fluctuations
  useEffect(() => {
    const interval = setInterval(() => {
      setListeners(prev => {
        const change = Math.floor(Math.random() * 20) - 10;
        return Math.max(1000, prev + change);
      });
    }, 15000);

    return () => clearInterval(interval);
  }, []);

  // Auto-advance tracks (simulate radio)
  useEffect(() => {
    if (!isPlaying) return;

    const currentDuration = currentTrack?.duration;
    if (!currentDuration) return;

    // Parse duration (e.g., "3:45" -> 225 seconds)
    const [minutes, seconds] = currentDuration.split(':').map(Number);
    const totalSeconds = (minutes * 60 + seconds) * 1000;

    const timer = setTimeout(() => {
      nextTrack();
    }, totalSeconds);

    return () => clearTimeout(timer);
  }, [isPlaying, currentTrack]);

  const nextTrack = () => {
    const nextIndex = (currentTrackIndex + 1) % EMPIRE_PLAYLIST.length;
    setCurrentTrackIndex(nextIndex);
    setCurrentTrack(EMPIRE_PLAYLIST[nextIndex]);
    
    console.log(`🎵 Now playing: ${EMPIRE_PLAYLIST[nextIndex].title} by ${EMPIRE_PLAYLIST[nextIndex].artist}`);
  };

  const previousTrack = () => {
    const prevIndex = currentTrackIndex === 0 ? EMPIRE_PLAYLIST.length - 1 : currentTrackIndex - 1;
    setCurrentTrackIndex(prevIndex);
    setCurrentTrack(EMPIRE_PLAYLIST[prevIndex]);
    
    console.log(`🎵 Now playing: ${EMPIRE_PLAYLIST[prevIndex].title} by ${EMPIRE_PLAYLIST[prevIndex].artist}`);
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
    console.log(isPlaying ? '⏸️ JUUWRAAYY RADIO paused' : '▶️ JUUWRAAYY RADIO playing');
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    console.log(isMuted ? '🔊 JUUWRAAYY RADIO unmuted' : '🔇 JUUWRAAYY RADIO muted');
  };

  const value: RadioContextType = {
    isPlaying,
    setIsPlaying,
    currentTrack,
    setCurrentTrack,
    volume,
    setVolume,
    isMuted,
    setIsMuted,
    isLive,
    listeners,
    playlist: EMPIRE_PLAYLIST,
    nextTrack,
    previousTrack,
    togglePlay,
    toggleMute
  };

  return (
    <RadioContext.Provider value={value}>
      {children}
    </RadioContext.Provider>
  );
}

export function useRadio() {
  const context = useContext(RadioContext);
  if (context === undefined) {
    throw new Error('useRadio must be used within a RadioProvider');
  }
  return context;
}
