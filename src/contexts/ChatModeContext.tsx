
import { createContext, useContext, useState, ReactNode } from "react";
import { AIProvider } from "@/lib/gremlinRouter";

export type ChatMode = "OG" | "GODMODE" | "ROAST" | "STEP" | "RAW";

interface ChatModeContextType {
  mode: ChatMode | null;
  setMode: (mode: ChatMode) => void;
  isStartupComplete: boolean;
  setStartupComplete: (isComplete: boolean) => void;
  aiProvider: AIProvider;
  setAiProvider: (provider: AIProvider) => void;
  isApiConnected: boolean;
  setIsApiConnected: (connected: boolean) => void;
}

const ChatModeContext = createContext<ChatModeContextType | undefined>(undefined);

export function ChatModeProvider({ children }: { children: ReactNode }) {
  const [mode, setMode] = useState<ChatMode | null>(null);
  const [isStartupComplete, setStartupComplete] = useState(false);
  const [aiProvider, setAiProvider] = useState<AIProvider>('openai');
  const [isApiConnected, setIsApiConnected] = useState(false);

  return (
    <ChatModeContext.Provider
      value={{
        mode,
        setMode,
        isStartupComplete,
        setStartupComplete,
        aiProvider,
        setAiProvider,
        isApiConnected,
        setIsApiConnected
      }}
    >
      {children}
    </ChatModeContext.Provider>
  );
}

export function useChatMode() {
  const context = useContext(ChatModeContext);
  if (context === undefined) {
    throw new Error("useChatMode must be used within a ChatModeProvider");
  }
  return context;
}
