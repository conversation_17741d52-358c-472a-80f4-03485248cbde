
import { createContext, useContext, useState, ReactNode } from "react";
import { AIProvider } from "@/lib/gremlinRouter";

export type ChatMode = "OG";

interface ChatModeContextType {
  mode: ChatMode;
  isStartupComplete: boolean;
  setStartupComplete: (isComplete: boolean) => void;
  aiProvider: AIProvider;
  setAiProvider: (provider: AIProvider) => void;
  isApiConnected: boolean;
  setIsApiConnected: (connected: boolean) => void;
}

const ChatModeContext = createContext<ChatModeContextType | undefined>(undefined);

export function ChatModeProvider({ children }: { children: ReactNode }) {
  const [isStartupComplete, setStartupComplete] = useState(false);
  const [aiProvider, setAiProvider] = useState<AIProvider>('openai');
  const [isApiConnected, setIsApiConnected] = useState(false);

  return (
    <ChatModeContext.Provider
      value={{
        mode: "OG", // Always OG mode
        isStartupComplete,
        setStartupComplete,
        aiProvider,
        setAiProvider,
        isApiConnected,
        setIsApiConnected
      }}
    >
      {children}
    </ChatModeContext.Provider>
  );
}

export function useChatMode() {
  const context = useContext(ChatModeContext);
  if (context === undefined) {
    throw new Error("useChatMode must be used within a ChatModeProvider");
  }
  return context;
}
