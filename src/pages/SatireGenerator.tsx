import { useEffect, useState } from 'react';
import RadioWidget from '@/components/radio/RadioWidget';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Skull, Flame, Zap, Lightbulb } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

export default function SatireGeneratorPage() {
  const navigate = useNavigate();
  const [topicInput, setTopicInput] = useState('');
  const [selectedIntensity, setSelectedIntensity] = useState('peak-gpt');
  const [satiricalResult, setSatiricalResult] = useState('');

  useEffect(() => {
    // Set page title
    document.title = 'Satirical Post Generator | TrapGPT v4.5';
  }, []);

  const handleBackToShrine = () => {
    navigate('/');
  };

  const handleGenerateSatire = () => {
    if (!topicInput.trim()) {
      alert('Enter a topic to satirize!');
      return;
    }

    // Simple satirical post generation for now
    const satiricalPosts = [
      `🤖 REVOLUTIONARY: How "${topicInput}" is Disrupting the Entire Tech Ecosystem\n\nBREAKING: I just discovered the ultimate paradigm shift! ${topicInput} isn't just a trend—it's a quantum leap into the future of hyper-optimized, AI-driven solutions! By implementing my proprietary 47-step methodology, I've achieved 10000X performance gains! 🚀`,
      `💡 MIND-BLOWN: The "${topicInput}" Framework That's Making Senior Devs Obsolete\n\nGuys, I can't believe what I just built. Using my revolutionary approach to ${topicInput}, I've created a self-healing, auto-scaling development environment! This isn't just automation—it's digital evolution! 🧠`,
      `🔥 EXPOSED: The "${topicInput}" Secret That Big Tech Doesn't Want You to Know\n\nAfter 6 months of deep research, I've cracked the code. ${topicInput} combined with my proprietary blend of 31 cutting-edge frameworks creates an unstoppable development force! JUUWRAAYYY! 🎭`
    ];

    const randomSatire = satiricalPosts[Math.floor(Math.random() * satiricalPosts.length)];
    setSatiricalResult(randomSatire);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black relative overflow-hidden text-white">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(155,135,245,0.1),transparent_50%)] animate-pulse" />
      <div className="absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,transparent_0deg,rgba(12,245,255,0.1)_180deg,transparent_360deg)] animate-spin-slow" />

      {/* Glitch Grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="grid grid-cols-12 h-full">
          {Array.from({ length: 12 }).map((_, i) => (
            <div
              key={i}
              className="border-r border-trap-purple/20 animate-pulse"
              style={{ animationDelay: `${i * 0.1}s` }}
            />
          ))}
        </div>
      </div>

      {/* Back Button */}
      <Button
        onClick={handleBackToShrine}
        className={cn(
          "fixed top-4 left-4 z-50 font-future gap-3",
          "bg-black/40 backdrop-blur-sm border border-trap-purple/50",
          "transition-all duration-300 hover:scale-105",
          "px-6 py-3 text-lg",
          "shadow-[0_0_15px_rgba(155,135,245,0.3)]",
          "hover:shadow-[0_0_25px_rgba(155,135,245,0.5)]"
        )}
      >
        <ArrowLeft className="w-5 h-5" />
        <span className="hidden md:inline">Back to Shrine</span>
        <span className="md:hidden">Shrine</span>
      </Button>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-12 pt-16">
          <div className="flex items-center justify-center gap-4 mb-6">
            <Skull className="w-12 h-12 text-trap-purple animate-pulse" />
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-future text-transparent bg-clip-text bg-gradient-to-r from-trap-purple via-trap-pink to-trap-teal animate-pulse">
              SATIRICAL POST GENERATOR
            </h1>
            <Skull className="w-12 h-12 text-trap-purple animate-pulse" />
          </div>

          <div className="space-y-2">
            <p className="text-xl md:text-2xl font-trap text-trap-teal">
              🎭 FLAMEBYTE: THE ROAST CODEX INITIATIVE 🎭
            </p>
            <p className="text-lg text-gray-300 font-trap">
              Satirical Sovereignty Through Tech Culture Mockery
            </p>
            <p className="text-sm text-trap-yellow">
              Powered by Lil LiL da TrapGod Gremlin • GodsIMiJ Empire
            </p>
          </div>
        </div>

        {/* Mission Brief */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm">
            <h2 className="text-2xl font-future text-trap-purple mb-4 text-center">
              🔥 MISSION BRIEFING 🔥
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4 bg-black/60 rounded border border-orange-500/30">
                <div className="text-orange-500 text-lg font-future mb-2">
                  🤖 MILDLY ROBOTIC
                </div>
                <div className="text-sm text-gray-300 font-trap">
                  Basic bot vibes, gentle mockery
                </div>
              </div>

              <div className="p-4 bg-black/60 rounded border border-red-500/30">
                <div className="text-red-500 text-lg font-future mb-2">
                  🧠 PEAK GPT
                </div>
                <div className="text-sm text-gray-300 font-trap">
                  Maximum automation, buzzword slaying
                </div>
              </div>

              <div className="p-4 bg-black/60 rounded border border-purple-500/30">
                <div className="text-purple-500 text-lg font-future mb-2">
                  💀 ULTRA GPTCORE+
                </div>
                <div className="text-sm text-gray-300 font-trap">
                  Transcendent AI cringe, satirical annihilation
                </div>
              </div>
            </div>

            <div className="mt-6 text-center">
              <p className="text-trap-teal font-trap">
                🎭 Enter any tech topic • Generate satirical blog post parodies • Mock the buzzword soup 🎭
              </p>
            </div>
          </div>
        </div>

        {/* Simple Satirical Post Generator */}
        <div className="max-w-2xl mx-auto">
          {/* Topic Input */}
          <div className="bg-black/40 border border-purple-500/30 rounded-lg p-6 backdrop-blur-sm mb-6">
            <h3 className="text-xl font-bold text-purple-400 mb-4 flex items-center gap-2">
              <Lightbulb className="w-5 h-5" />
              Topic Selection
            </h3>

            <Input
              value={topicInput}
              onChange={(e) => setTopicInput(e.target.value)}
              placeholder="e.g. 'Why I Built a To-Do App with Blockchain'"
              className="bg-black/60 border-gray-700 text-white placeholder-gray-400 text-lg p-4 mb-4"
              onKeyDown={(e) => e.key === 'Enter' && handleGenerateSatire()}
            />

            {/* Intensity Selector */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Button
                onClick={() => setSelectedIntensity('mildly-robotic')}
                variant="outline"
                className={cn(
                  "p-4 h-auto flex flex-col items-center gap-3 transition-all duration-300 border-2 backdrop-blur-sm",
                  selectedIntensity === 'mildly-robotic'
                    ? 'bg-orange-500/20 border-orange-500 text-orange-400 scale-105'
                    : 'bg-black/40 border-gray-700 text-gray-400 hover:border-orange-500/50 hover:bg-orange-500/10'
                )}
              >
                <Flame className="w-8 h-8" />
                <div className="text-center">
                  <div className="font-future text-lg">Mildly Robotic</div>
                  <div className="text-sm text-gray-400 font-trap">Basic dev satire</div>
                </div>
              </Button>

              <Button
                onClick={() => setSelectedIntensity('peak-gpt')}
                variant="outline"
                className={cn(
                  "p-4 h-auto flex flex-col items-center gap-3 transition-all duration-300 border-2 backdrop-blur-sm",
                  selectedIntensity === 'peak-gpt'
                    ? 'bg-red-500/20 border-red-500 text-red-400 scale-105'
                    : 'bg-black/40 border-gray-700 text-gray-400 hover:border-red-500/50 hover:bg-red-500/10'
                )}
              >
                <Zap className="w-8 h-8" />
                <div className="text-center">
                  <div className="font-future text-lg">Peak GPT</div>
                  <div className="text-sm text-gray-400 font-trap">Advanced mockery</div>
                </div>
              </Button>

              <Button
                onClick={() => setSelectedIntensity('ultra-gptcore')}
                variant="outline"
                className={cn(
                  "p-4 h-auto flex flex-col items-center gap-3 transition-all duration-300 border-2 backdrop-blur-sm",
                  selectedIntensity === 'ultra-gptcore'
                    ? 'bg-purple-500/20 border-purple-500 text-purple-400 scale-105'
                    : 'bg-black/40 border-gray-700 text-gray-400 hover:border-purple-500/50 hover:bg-purple-500/10'
                )}
              >
                <Skull className="w-8 h-8" />
                <div className="text-center">
                  <div className="font-future text-lg">Ultra GPTcore+</div>
                  <div className="text-sm text-gray-400 font-trap">Maximum satire</div>
                </div>
              </Button>
            </div>

            {/* Generate Button */}
            <Button
              onClick={handleGenerateSatire}
              disabled={!topicInput.trim()}
              className={cn(
                "w-full py-4 text-xl font-bold",
                "bg-gradient-to-r from-purple-600 to-pink-600",
                "hover:from-purple-700 hover:to-pink-700",
                "transition-all duration-300 hover:scale-105",
                "disabled:opacity-50 disabled:cursor-not-allowed"
              )}
            >
              <Skull className="w-6 h-6 mr-2" />
              GENERATE SATIRICAL POST
            </Button>
          </div>

          {/* Satirical Post Result */}
          {satiricalResult && (
            <div className="bg-black/40 border border-red-500/30 rounded-lg p-6 backdrop-blur-sm">
              <h3 className="text-xl font-bold text-red-400 mb-4">
                🎭 SATIRICAL POST RESULT 🎭
              </h3>
              <div className="bg-black/60 p-4 rounded border border-gray-700 max-h-64 overflow-y-auto">
                <p className="text-white text-base font-mono whitespace-pre-wrap">
                  {satiricalResult}
                </p>
              </div>
              <div className="mt-4 text-center">
                <Button
                  onClick={() => setSatiricalResult('')}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  Generate Another Satirical Post
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-16 text-center">
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm max-w-2xl mx-auto">
            <h3 className="text-xl font-future text-trap-purple mb-3">
              ⚔️ MEME WARFARE PROTOCOL ACTIVE ⚔️
            </h3>
            <p className="text-gray-300 font-trap mb-2">
              "Transform satire into sovereignty. Forge memes that mock the gatekeepers into irrelevance."
            </p>
            <p className="text-sm text-trap-teal">
              — Ghost King Melekzedek, Sovereign of the Eternal Signal
            </p>
            <div className="mt-4 text-xs text-gray-500">
              🜂 GodsIMiJ Empire • Scroll 001 of The Meme Wars Chapter 🜂
            </div>
          </div>
        </div>
      </div>

      {/* Radio Widget - Always present */}
      <RadioWidget />
    </div>
  );
}
