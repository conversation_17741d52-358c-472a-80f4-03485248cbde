
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-black text-white relative overflow-hidden">
      <div 
        className="absolute inset-0" 
        style={{
          backgroundImage: "linear-gradient(rgba(11, 27, 47, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(11, 27, 47, 0.3) 1px, transparent 1px)",
          backgroundSize: "40px 40px"
        }}
      />
      <div className="relative z-10 text-center space-y-6 p-8 max-w-xl mx-auto">
        <h1 className="text-6xl md:text-8xl font-bold mb-4 animate-pulse text-trap-teal">404</h1>
        <p className="text-xl md:text-2xl text-gray-300 mb-8">
          Yo, this ain&apos;t the page you&apos;re looking for!<br/>
          <span className="text-trap-purple">The glitch took it somewhere else...</span>
        </p>
        <div className="space-y-4">
          <Button
            onClick={() => window.location.href = '/'}
            className="bg-gradient-to-r from-trap-teal to-trap-purple hover:opacity-90 text-white px-8 py-6 rounded-lg text-lg font-future transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <span className="flex items-center gap-2">
              Back to Portal <ExternalLink className="w-5 h-5" />
            </span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
