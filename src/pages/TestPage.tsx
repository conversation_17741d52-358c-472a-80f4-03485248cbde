import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function TestPage() {
  const navigate = useNavigate();

  const handleBackToShrine = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-black text-white p-8">
      {/* Back Button */}
      <Button
        onClick={handleBackToShrine}
        className="mb-8 bg-purple-600 hover:bg-purple-700"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Shrine
      </Button>

      {/* Test Content */}
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-6xl font-bold text-purple-500 mb-8">
          🔥 TEST PAGE 🔥
        </h1>
        
        <div className="bg-gray-800 p-8 rounded-lg mb-8">
          <h2 className="text-3xl text-green-400 mb-4">
            ✅ PAGE LOADED SUCCESSFULLY!
          </h2>
          <p className="text-xl text-white mb-4">
            If you can see this, the routing is working correctly.
          </p>
          <p className="text-lg text-gray-300">
            This means the issue is with the RoastGenerator component specifically.
          </p>
        </div>

        <div className="bg-red-900 p-6 rounded-lg">
          <h3 className="text-2xl text-red-400 mb-4">
            🐛 DEBUGGING INFO
          </h3>
          <p className="text-white">
            Current URL: {window.location.href}
          </p>
          <p className="text-white">
            Current Path: {window.location.pathname}
          </p>
        </div>
      </div>
    </div>
  );
}
