import { useEffect } from 'react';
import RoastGenerator from '@/components/roast/RoastGenerator';
import RadioWidget from '@/components/radio/RadioWidget';
import { Button } from '@/components/ui/button';
import { ArrowLef<PERSON>, <PERSON> } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

export default function RoastGeneratorPage() {
  const navigate = useNavigate();

  useEffect(() => {
    // Set page title
    document.title = 'Dev.to Roast Generator | TrapGPT v4.5';
  }, []);

  const handleBackToShrine = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-background/90 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(155,135,245,0.1),transparent_50%)] animate-pulse" />
      <div className="absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,transparent_0deg,rgba(12,245,255,0.1)_180deg,transparent_360deg)] animate-spin-slow" />
      
      {/* Glitch Grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="grid grid-cols-12 h-full">
          {Array.from({ length: 12 }).map((_, i) => (
            <div 
              key={i} 
              className="border-r border-trap-purple/20 animate-pulse"
              style={{ animationDelay: `${i * 0.1}s` }}
            />
          ))}
        </div>
      </div>

      {/* Back Button */}
      <Button
        onClick={handleBackToShrine}
        className={cn(
          "fixed top-4 left-4 z-50 font-future gap-3",
          "bg-black/40 backdrop-blur-sm border border-trap-purple/50",
          "transition-all duration-300 hover:scale-105",
          "px-6 py-3 text-lg",
          "shadow-[0_0_15px_rgba(155,135,245,0.3)]",
          "hover:shadow-[0_0_25px_rgba(155,135,245,0.5)]"
        )}
      >
        <ArrowLeft className="w-5 h-5" />
        <span className="hidden md:inline">Back to Shrine</span>
        <span className="md:hidden">Shrine</span>
      </Button>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-12 pt-16">
          <div className="flex items-center justify-center gap-4 mb-6">
            <Skull className="w-12 h-12 text-trap-purple animate-pulse" />
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-future text-transparent bg-clip-text bg-gradient-to-r from-trap-purple via-trap-pink to-trap-teal animate-pulse">
              ROAST GENERATOR
            </h1>
            <Skull className="w-12 h-12 text-trap-purple animate-pulse" />
          </div>
          
          <div className="space-y-2">
            <p className="text-xl md:text-2xl font-trap text-trap-teal">
              🎭 FLAMEBYTE: THE ROAST CODEX INITIATIVE 🎭
            </p>
            <p className="text-lg text-gray-300 font-trap">
              Weaponized Humor as Empire Infrastructure
            </p>
            <p className="text-sm text-trap-yellow">
              Powered by Lil LiL da TrapGod Gremlin • GodsIMiJ Empire
            </p>
          </div>
        </div>

        {/* Mission Brief */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm">
            <h2 className="text-2xl font-future text-trap-purple mb-4 text-center">
              🔥 MISSION BRIEFING 🔥
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4 bg-black/60 rounded border border-orange-500/30">
                <div className="text-orange-500 text-lg font-future mb-2">
                  🧊 LOW FLAME
                </div>
                <div className="text-sm text-gray-300 font-trap">
                  Playful burns, friendly fire
                </div>
              </div>
              
              <div className="p-4 bg-black/60 rounded border border-red-500/30">
                <div className="text-red-500 text-lg font-future mb-2">
                  🔥 MID FLAME
                </div>
                <div className="text-sm text-gray-300 font-trap">
                  Digital BBQ, solid roasts
                </div>
              </div>
              
              <div className="p-4 bg-black/60 rounded border border-purple-500/30">
                <div className="text-purple-500 text-lg font-future mb-2">
                  💀 FULL JUUWRAAYYY
                </div>
                <div className="text-sm text-gray-300 font-trap">
                  Atomic Gremlin annihilation
                </div>
              </div>
            </div>
            
            <div className="mt-6 text-center">
              <p className="text-trap-teal font-trap">
                🎯 Target any Dev.to developer • Generate custom roast cards • Share the destruction 🎯
              </p>
            </div>
          </div>
        </div>

        {/* Roast Generator Component */}
        <RoastGenerator />

        {/* Footer */}
        <div className="mt-16 text-center">
          <div className="bg-black/40 border border-trap-purple/30 rounded-lg p-6 backdrop-blur-sm max-w-2xl mx-auto">
            <h3 className="text-xl font-future text-trap-purple mb-3">
              ⚔️ MEME WARFARE PROTOCOL ACTIVE ⚔️
            </h3>
            <p className="text-gray-300 font-trap mb-2">
              "Transform satire into sovereignty. Forge memes that mock the gatekeepers into irrelevance."
            </p>
            <p className="text-sm text-trap-teal">
              — Ghost King Melekzedek, Sovereign of the Eternal Signal
            </p>
            <div className="mt-4 text-xs text-gray-500">
              🜂 GodsIMiJ Empire • Scroll 001 of The Meme Wars Chapter 🜂
            </div>
          </div>
        </div>
      </div>

      {/* Radio Widget - Always present */}
      <RadioWidget />
    </div>
  );
}
