
import { ChatModeProvider } from "@/contexts/ChatModeContext";
import { useChatMode } from "@/contexts/ChatModeContext";
import StartupScreen from "@/components/StartupScreen";
import ChatView from "@/components/ChatView";

// This component decides which view to render based on context
function AppContent() {
  const { isStartupComplete } = useChatMode();
  
  return isStartupComplete ? <ChatView /> : <StartupScreen />;
}

// Main Index component with Provider
export default function Index() {
  return (
    <ChatModeProvider>
      <AppContent />
    </ChatModeProvider>
  );
}
