
import { useState, useEffect } from 'react';
import { nanoid } from 'nanoid';

export function useDeviceId() {
  const [deviceId, setDeviceId] = useState<string>('');

  useEffect(() => {
    // Try to get existing device ID from localStorage
    let id = localStorage.getItem('device_id');
    
    // If no device ID exists, create one and store it
    if (!id) {
      id = nanoid();
      localStorage.setItem('device_id', id);
    }
    
    setDeviceId(id);
  }, []);

  return deviceId;
}
