@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=VT323&family=Orbitron:wght@400;600&display=swap');

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 260 60% 75%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 183 100% 50%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    --radius: 0.5rem;
    
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-black text-foreground overflow-x-hidden;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Cyberpunk fonts */
  .font-trap {
    font-family: 'VT323', monospace;
  }
  
  .font-future {
    font-family: 'Orbitron', sans-serif;
  }
}

@layer utilities {
  .text-glow {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }
  
  .box-glow {
    box-shadow: 0 0 5px currentColor, 0 0 10px currentColor;
  }
  
  .noise {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.6' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='400' height='400' filter='url(%23noiseFilter)' opacity='0.1'/%3E%3C/svg%3E");
  }

  .holographic {
    @apply relative overflow-hidden;
    background: linear-gradient(
      135deg,
      rgba(255,255,255,0.1) 0%,
      rgba(255,255,255,0.05) 100%
    );
    backdrop-filter: blur(10px);
  }

  .holographic::before {
    content: '';
    @apply absolute inset-0;
    background: linear-gradient(
      45deg,
      transparent 0%,
      rgba(255,255,255,0.1) 50%,
      transparent 100%
    );
    transform: translateX(-100%);
    transition: transform 0.5s;
  }

  .holographic:hover::before {
    transform: translateX(100%);
  }

  .text-glow-teal {
    text-shadow: 0 0 10px rgba(12,229,255,0.5);
  }
  
  .text-glow-purple {
    text-shadow: 0 0 10px rgba(155,135,245,0.5);
  }
  
  .hover-float {
    transition: transform 0.3s ease;
  }
  
  .hover-float:hover {
    transform: translateY(-5px);
  }
}
