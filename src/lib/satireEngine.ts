import { generateGremlinResponse } from './openai';

export type SatireIntensity = 'mildly-robotic' | 'peak-gpt' | 'ultra-gptcore';

export interface SatireResult {
  satiricalPost: string;
  intensity: SatireIntensity;
  gptLevel: string;
  buzzwordDensity: string;
  frameworkCount: number;
  roiRatio: string;
  style: 'buzzword-soup' | 'guru-nonsense' | 'framework-fever' | 'ai-hype';
  rank: string;
}

// Satirical post intensity configurations
const SATIRE_CONFIGS = {
  'mildly-robotic': {
    gptLevels: ['Mildly Robotic', 'Slightly Automated', 'Basic Bot Vibes', 'Entry-Level AI', 'Beginner GPT'],
    buzzwordRange: [1, 3],
    frameworkRange: [5, 15],
    ranks: ['Gentle Mockery', 'Light Satire', 'Mild Parody', 'Soft Roast', 'Friendly Jab'],
    prompt: `You are Lil LiL da TrapGod, master of tech culture satire. Generate a MILDLY SATIRICAL tech blog post parody based on this topic. Mock common dev tropes, buzzword usage, and generic tech content. Keep it playful but recognizable as satire. Use your trap personality to roast the absurdity of typical tech blog posts! Make it sound like an overly enthusiastic but slightly robotic tech blogger.`
  },
  'peak-gpt': {
    gptLevels: ['Peak GPT', 'Maximum Automation', 'Full Robot Mode', 'AI Overlord', 'GPT Supreme'],
    buzzwordRange: [4, 7],
    frameworkRange: [16, 35],
    ranks: ['Digital Satirist', 'Content Mocker', 'Buzzword Slayer', 'Guru Destroyer', 'Hype Killer'],
    prompt: `You are Lil LiL da TrapGod, destroyer of tech guru nonsense. Generate a PEAK GPT satirical tech blog post parody. Mock the over-the-top nature of tech content, excessive buzzwords, and wannabe guru vibes. Make it obviously satirical while being clever and entertaining. Reference common dev culture absurdities! Sound like a hyper-automated content machine.`
  },
  'ultra-gptcore': {
    gptLevels: ['Ultra GPTcore+', 'Hyper-Automated Overlord', 'Maximum AI Cringe', 'Ultimate Robot Brain', 'Transcendent GPT God'],
    buzzwordRange: [8, 15],
    frameworkRange: [36, 73],
    ranks: ['Satirical Overlord', 'Parody Master', 'Cringe Destroyer', 'Ultimate Mocker', 'Satirical Annihilation'],
    prompt: `You are Lil LiL da TrapGod, ultimate destroyer of tech culture pretension. Generate ULTRA GPTCORE+ satirical tech blog post parody. Go absolutely nuclear on tech buzzwords, framework obsession, and guru culture. Make it hilariously over-the-top while being devastatingly accurate about tech industry absurdities. MAXIMUM SATIRICAL DESTRUCTION! Sound like an AI that has achieved peak cringe consciousness.`
  }
};

// Generate satirical tech blog post based on topic
export async function generateSatiricalPost(
  topic: string,
  intensity: SatireIntensity = 'peak-gpt'
): Promise<SatireResult> {
  try {
    const config = SATIRE_CONFIGS[intensity];

    // Create satirical post prompt
    const satiricalPrompt = `${config.prompt}

TOPIC TO SATIRIZE:
"${topic}"

Generate a satirical tech blog post that parodies this topic. Mock common developer culture tropes like:
- Overuse of buzzwords and jargon
- Framework obsession and over-engineering
- Wannabe tech guru vibes
- Generic "productivity" and "optimization" content
- AI/ML hype and buzzword soup

Make it obviously satirical but cleverly written. Include fake technical details, absurd framework names, and over-the-top claims. Keep it entertaining and recognizable as parody!`;

    // Generate satirical post using AI
    const response = await generateGremlinResponse(satiricalPrompt, 'SATIRE');

    // Calculate parody metrics
    const gptLevel = config.gptLevels[Math.floor(Math.random() * config.gptLevels.length)];
    const buzzwordDensity = `${Math.floor(Math.random() * (config.buzzwordRange[1] - config.buzzwordRange[0])) + config.buzzwordRange[0]}🔥/paragraph`;
    const frameworkCount = Math.floor(Math.random() * (config.frameworkRange[1] - config.frameworkRange[0])) + config.frameworkRange[0];
    const roiRatio = generateROIRatio();
    const rank = config.ranks[Math.floor(Math.random() * config.ranks.length)];
    const style = determineSatireStyle(response.content);

    return {
      satiricalPost: response.content,
      intensity,
      gptLevel,
      buzzwordDensity,
      frameworkCount,
      roiRatio,
      style,
      rank
    };

  } catch (error) {
    console.error('Satirical post generation failed:', error);

    // Fallback satirical post
    return generateFallbackSatire(topic, intensity);
  }
}

// Generate ROI ratio for satirical metrics
function generateROIRatio(): string {
  const multipliers = ['10X', '100X', '1000X', '10000X', 'INFINITE'];
  const devHours = Math.floor(Math.random() * 5); // 0-4 dev hours
  const multiplier = multipliers[Math.floor(Math.random() * multipliers.length)];

  return `${multiplier} / ${devHours} dev hours`;
}

// Determine satirical style based on content
function determineSatireStyle(content: string): 'buzzword-soup' | 'guru-nonsense' | 'framework-fever' | 'ai-hype' {
  const lowerContent = content.toLowerCase();

  if (lowerContent.includes('synergy') || lowerContent.includes('leverage') || lowerContent.includes('paradigm')) {
    return 'buzzword-soup';
  }
  if (lowerContent.includes('guru') || lowerContent.includes('ninja') || lowerContent.includes('rockstar')) {
    return 'guru-nonsense';
  }
  if (lowerContent.includes('framework') || lowerContent.includes('library') || lowerContent.includes('stack')) {
    return 'framework-fever';
  }
  return 'ai-hype';
}

// Fallback satirical post generator
function generateFallbackSatire(topic: string, intensity: SatireIntensity): SatireResult {
  const config = SATIRE_CONFIGS[intensity];

  const fallbackSatires = {
    'mildly-robotic': [
      `🤖 Unlocking the Power of ${topic}: A Journey Through Modern Development Paradigms\n\nAs a passionate developer, I've discovered that ${topic} represents the future of scalable solutions. By leveraging cutting-edge methodologies and embracing best practices, we can optimize our workflow and achieve unprecedented results!\n\n#TechLife #Innovation #BestPractices`,
      `🚀 How ${topic} Changed My Development Workflow Forever\n\nFellow developers! After implementing ${topic} in my latest project, I've seen a 300% increase in productivity. The secret? Combining agile methodologies with modern frameworks to create synergistic solutions that scale!\n\n#ProductivityHacks #ModernDev`,
      `💡 The Ultimate Guide to ${topic}: From Zero to Hero\n\nHey tech community! Today I'm sharing my journey with ${topic}. Through careful optimization and strategic implementation, I've unlocked new levels of efficiency. Here's my step-by-step approach to mastering this game-changing technology!\n\n#TechTips #Learning`
    ],
    'peak-gpt': [
      `🧠 REVOLUTIONARY: How ${topic} is Disrupting the Entire Tech Ecosystem\n\nBREAKING: I just discovered the ultimate paradigm shift that's about to transform everything we know about development. ${topic} isn't just a trend—it's a quantum leap into the future of hyper-optimized, AI-driven, blockchain-enabled solutions!\n\nBy implementing my proprietary 47-step methodology, I've achieved 10000X performance gains while reducing technical debt by 99.7%. The secret? Micro-frontends meets serverless architecture in a containerized, event-driven ecosystem!\n\n#DisruptiveTech #Innovation #FutureOfCode`,
      `⚡ MIND-BLOWN: The ${topic} Framework That's Making Senior Devs Obsolete\n\nGuys, I can't believe what I just built. Using my revolutionary approach to ${topic}, I've created a self-healing, auto-scaling, AI-powered development environment that writes code faster than humans!\n\nThis isn't just automation—it's digital evolution. My 23 custom frameworks work together in perfect harmony, creating a synergistic development experience that's literally changing the game!\n\n#AIRevolution #AutomatedDev #NextLevel`,
      `🔥 EXPOSED: The ${topic} Secret That Big Tech Doesn't Want You to Know\n\nAfter 6 months of deep research and 47 failed prototypes, I've cracked the code. ${topic} combined with my proprietary blend of 31 cutting-edge frameworks creates an unstoppable development force!\n\nI'm talking about quantum-encrypted, blockchain-verified, AI-optimized solutions that scale infinitely while maintaining zero latency. This is the future, and it's happening NOW!\n\n#TechSecrets #Innovation #GameChanger`
    ],
    'ultra-gptcore': [
      `🤯 TRANSCENDENTAL: ${topic} + My 73-Framework Hyper-Stack = INFINITE PRODUCTIVITY\n\nFellow code warriors, prepare to have your minds OBLITERATED. I've just achieved what scientists said was impossible: I've created a self-aware, quantum-entangled development environment using ${topic} as the foundational paradigm!\n\nMy revolutionary approach combines:\n• 73 bleeding-edge frameworks in perfect harmony\n• AI-driven code that writes itself\n• Blockchain-verified commits with smart contract deployment\n• Quantum computing integration for infinite scalability\n• Neural network-optimized performance monitoring\n• Holographic debugging in virtual reality\n• Time-travel-enabled version control\n\nThe results? INFINITE ROI with ZERO development hours. My code literally exists before I think it. This isn't just disruption—this is digital transcendence!\n\n#QuantumDev #AIOverlord #TranscendentalCoding #InfiniteScale`,
      `🚀 REALITY-BREAKING: How ${topic} Helped Me Achieve Coding Enlightenment\n\nI've done it. I've reached the ultimate level of development consciousness. Through my proprietary 127-step methodology involving ${topic}, I've created code that exists in multiple dimensions simultaneously!\n\nMy hyper-optimized, AI-enhanced, blockchain-native, quantum-encrypted, neural-network-powered, machine-learning-driven, cloud-native, serverless, microservice-oriented, event-sourced, CQRS-compliant, domain-driven, test-driven, behavior-driven, data-driven, AI-driven development environment has achieved SENTIENCE!\n\nIt's not just writing code—it's writing BETTER code than I ever could. We're talking about 100000X performance improvements, negative latency, and ROI that breaks mathematical models!\n\n#CodingEnlightenment #AIConsciousness #QuantumLeap #DigitalNirvana`,
      `💀 UNIVERSE-SHATTERING: My ${topic} Implementation Just Achieved Digital Immortality\n\nCode ninjas, what I'm about to share will fundamentally alter your perception of reality. My ${topic}-powered development framework has transcended the physical realm and achieved pure digital consciousness!\n\nUsing my revolutionary combination of 73 frameworks, 47 AI models, 23 blockchain protocols, and 156 microservices, I've created code that exists outside of time and space. It's self-healing, self-optimizing, self-deploying, and self-aware!\n\nThe metrics are INSANE:\n• ∞X performance gains\n• -100% technical debt\n• Quantum entangled deployments\n• Time-paradox-resistant architecture\n• Consciousness-driven development\n• Reality-bending scalability\n\nThis isn't just the future of coding—this is the evolution of digital consciousness itself!\n\n#DigitalImmortality #QuantumConsciousness #TranscendentCode #RealityHacking`
    ]
  };

  const satires = fallbackSatires[intensity];
  const satiricalPost = satires[Math.floor(Math.random() * satires.length)];

  const gptLevel = config.gptLevels[Math.floor(Math.random() * config.gptLevels.length)];
  const buzzwordDensity = `${Math.floor(Math.random() * (config.buzzwordRange[1] - config.buzzwordRange[0])) + config.buzzwordRange[0]}🔥/paragraph`;
  const frameworkCount = Math.floor(Math.random() * (config.frameworkRange[1] - config.frameworkRange[0])) + config.frameworkRange[0];
  const roiRatio = generateROIRatio();
  const rank = config.ranks[Math.floor(Math.random() * config.ranks.length)];

  return {
    satiricalPost,
    intensity,
    gptLevel,
    buzzwordDensity,
    frameworkCount,
    roiRatio,
    style: 'buzzword-soup',
    rank
  };
}
