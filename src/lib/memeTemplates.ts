export interface MemeTemplate {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  textAreas: TextArea[];
  category: 'classic' | 'trap' | 'dev' | 'satire';
}

export interface TextArea {
  id: string;
  x: number; // percentage from left
  y: number; // percentage from top
  width: number; // percentage
  height: number; // percentage
  fontSize: number;
  fontFamily: string;
  color: string;
  textAlign: 'left' | 'center' | 'right';
  maxLength?: number;
  placeholder: string;
}

// Meme Template Matrix (MTM) - Sacred Arsenal of Visual Warfare
export const MEME_TEMPLATES: MemeTemplate[] = [
  {
    id: 'drake-pointing',
    name: '<PERSON> Pointing',
    description: 'Classic Drake approval/disapproval format',
    imageUrl: '/meme-templates/drake-template.jpg',
    category: 'classic',
    textAreas: [
      {
        id: 'top-text',
        x: 50,
        y: 25,
        width: 45,
        height: 20,
        fontSize: 24,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 50,
        placeholder: '<PERSON> <PERSON> disapproves of'
      },
      {
        id: 'bottom-text',
        x: 50,
        y: 75,
        width: 45,
        height: 20,
        fontSize: 24,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 50,
        placeholder: 'Thing Drake approves of'
      }
    ]
  },
  {
    id: 'expanding-brain',
    name: 'Expanding Brain',
    description: 'Four-level brain expansion meme',
    imageUrl: '/meme-templates/brain-template.jpg',
    category: 'classic',
    textAreas: [
      {
        id: 'level-1',
        x: 55,
        y: 12,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Basic level'
      },
      {
        id: 'level-2',
        x: 55,
        y: 32,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Intermediate level'
      },
      {
        id: 'level-3',
        x: 55,
        y: 52,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Advanced level'
      },
      {
        id: 'level-4',
        x: 55,
        y: 72,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Galaxy brain level'
      }
    ]
  },
  {
    id: 'trapgod-satire',
    name: 'TrapGod Satirical Post Card',
    description: 'Custom TrapGPT satirical post card format',
    imageUrl: '/meme-templates/trapgod-satire-template.jpg',
    category: 'trap',
    textAreas: [
      {
        id: 'topic-title',
        x: 50,
        y: 15,
        width: 80,
        height: 10,
        fontSize: 24,
        fontFamily: 'Orbitron, sans-serif',
        color: '#0cf5ff',
        textAlign: 'center',
        maxLength: 50,
        placeholder: 'Tech Topic Being Satirized'
      },
      {
        id: 'satirical-text',
        x: 50,
        y: 45,
        width: 85,
        height: 30,
        fontSize: 18,
        fontFamily: 'VT323, monospace',
        color: '#ffffff',
        textAlign: 'center',
        maxLength: 300,
        placeholder: 'Satirical post content goes here'
      },
      {
        id: 'gpt-level',
        x: 15,
        y: 80,
        width: 20,
        height: 8,
        fontSize: 14,
        fontFamily: 'Orbitron, sans-serif',
        color: '#ff6b6b',
        textAlign: 'center',
        maxLength: 15,
        placeholder: 'PEAK GPT'
      },
      {
        id: 'framework-count',
        x: 40,
        y: 80,
        width: 20,
        height: 8,
        fontSize: 14,
        fontFamily: 'Orbitron, sans-serif',
        color: '#ffd700',
        textAlign: 'center',
        maxLength: 10,
        placeholder: '47 FRAMEWORKS'
      },
      {
        id: 'buzzword-density',
        x: 65,
        y: 80,
        width: 20,
        height: 8,
        fontSize: 14,
        fontFamily: 'Orbitron, sans-serif',
        color: '#00ff00',
        textAlign: 'center',
        maxLength: 15,
        placeholder: '8🔥/paragraph'
      },
      {
        id: 'rank',
        x: 85,
        y: 80,
        width: 15,
        height: 8,
        fontSize: 14,
        fontFamily: 'Orbitron, sans-serif',
        color: '#9b87f5',
        textAlign: 'center',
        maxLength: 20,
        placeholder: 'SATIRICAL OVERLORD'
      }
    ]
  },
  {
    id: 'dev-struggle',
    name: 'Developer Struggle',
    description: 'Relatable developer pain points',
    imageUrl: '/meme-templates/dev-struggle-template.jpg',
    category: 'dev',
    textAreas: [
      {
        id: 'problem',
        x: 50,
        y: 20,
        width: 80,
        height: 25,
        fontSize: 22,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 80,
        placeholder: 'The coding problem'
      },
      {
        id: 'solution',
        x: 50,
        y: 70,
        width: 80,
        height: 25,
        fontSize: 22,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 80,
        placeholder: 'The "solution"'
      }
    ]
  },
  {
    id: 'gremlin-wisdom',
    name: 'Gremlin Wisdom',
    description: 'Lil LiL dispensing trap wisdom',
    imageUrl: '/meme-templates/gremlin-wisdom-template.jpg',
    category: 'trap',
    textAreas: [
      {
        id: 'wisdom-text',
        x: 50,
        y: 75,
        width: 90,
        height: 20,
        fontSize: 24,
        fontFamily: 'VT323, monospace',
        color: '#0cf5ff',
        textAlign: 'center',
        maxLength: 100,
        placeholder: 'Gremlin wisdom goes here'
      }
    ]
  },
  {
    id: 'code-review',
    name: 'Code Review Reaction',
    description: 'Reactions to code reviews',
    imageUrl: '/meme-templates/code-review-template.jpg',
    category: 'dev',
    textAreas: [
      {
        id: 'review-comment',
        x: 50,
        y: 15,
        width: 80,
        height: 15,
        fontSize: 20,
        fontFamily: 'Arial, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 60,
        placeholder: 'Code review comment'
      },
      {
        id: 'developer-reaction',
        x: 50,
        y: 80,
        width: 80,
        height: 15,
        fontSize: 20,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 60,
        placeholder: 'Developer reaction'
      }
    ]
  }
];

// Generate meme content based on satirical post and metrics
export function generateMemeContent(
  templateId: string,
  satiricalText: string,
  topic: string,
  gptLevel: string,
  frameworkCount: number,
  buzzwordDensity: string,
  rank: string
): Record<string, string> {
  const template = MEME_TEMPLATES.find(t => t.id === templateId);
  if (!template) return {};

  const content: Record<string, string> = {};

  switch (templateId) {
    case 'trapgod-satire':
      content['topic-title'] = topic;
      content['satirical-text'] = satiricalText.substring(0, 300); // Truncate for display
      content['gpt-level'] = gptLevel;
      content['framework-count'] = `${frameworkCount} FRAMEWORKS`;
      content['buzzword-density'] = buzzwordDensity;
      content['rank'] = rank;
      break;

    case 'drake-pointing':
      content['top-text'] = `Traditional ${topic} approach`;
      content['bottom-text'] = `My revolutionary ${topic} framework`;
      break;

    case 'expanding-brain':
      content['level-1'] = `Basic ${topic} implementation`;
      content['level-2'] = `${topic} with 5 frameworks`;
      content['level-3'] = `${topic} with AI and blockchain`;
      content['level-4'] = `${topic} with ${frameworkCount} frameworks + quantum computing`;
      break;

    case 'dev-struggle':
      content['problem'] = `Writing about ${topic}`;
      content['solution'] = `Adding more buzzwords and frameworks`;
      break;

    case 'gremlin-wisdom':
      content['wisdom-text'] = `"${topic} is just another way to over-engineer everything" - Lil LiL da TrapGod`;
      break;

    case 'code-review':
      content['review-comment'] = `This ${topic} blog post needs more buzzwords`;
      content['developer-reaction'] = `*adds 47 more frameworks*`;
      break;

    default:
      // Generic fallback
      if (template.textAreas.length > 0) {
        content[template.textAreas[0].id] = satiricalText.substring(0, 100);
      }
  }

  return content;
}

// Get templates by category
export function getTemplatesByCategory(category: MemeTemplate['category']): MemeTemplate[] {
  return MEME_TEMPLATES.filter(template => template.category === category);
}

// Get random template
export function getRandomTemplate(): MemeTemplate {
  return MEME_TEMPLATES[Math.floor(Math.random() * MEME_TEMPLATES.length)];
}

// Get template by ID
export function getTemplateById(id: string): MemeTemplate | undefined {
  return MEME_TEMPLATES.find(template => template.id === id);
}
