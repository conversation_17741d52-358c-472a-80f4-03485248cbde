export interface MemeTemplate {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  textAreas: TextArea[];
  category: 'classic' | 'trap' | 'dev' | 'roast';
}

export interface TextArea {
  id: string;
  x: number; // percentage from left
  y: number; // percentage from top
  width: number; // percentage
  height: number; // percentage
  fontSize: number;
  fontFamily: string;
  color: string;
  textAlign: 'left' | 'center' | 'right';
  maxLength?: number;
  placeholder: string;
}

// Meme Template Matrix (MTM) - Sacred Arsenal of Visual Warfare
export const MEME_TEMPLATES: MemeTemplate[] = [
  {
    id: 'drake-pointing',
    name: '<PERSON> Pointing',
    description: 'Classic Drake approval/disapproval format',
    imageUrl: '/meme-templates/drake-template.jpg',
    category: 'classic',
    textAreas: [
      {
        id: 'top-text',
        x: 50,
        y: 25,
        width: 45,
        height: 20,
        fontSize: 24,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 50,
        placeholder: '<PERSON> <PERSON> disapproves of'
      },
      {
        id: 'bottom-text',
        x: 50,
        y: 75,
        width: 45,
        height: 20,
        fontSize: 24,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 50,
        placeholder: 'Thing Drake approves of'
      }
    ]
  },
  {
    id: 'expanding-brain',
    name: 'Expanding Brain',
    description: 'Four-level brain expansion meme',
    imageUrl: '/meme-templates/brain-template.jpg',
    category: 'classic',
    textAreas: [
      {
        id: 'level-1',
        x: 55,
        y: 12,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Basic level'
      },
      {
        id: 'level-2',
        x: 55,
        y: 32,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Intermediate level'
      },
      {
        id: 'level-3',
        x: 55,
        y: 52,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Advanced level'
      },
      {
        id: 'level-4',
        x: 55,
        y: 72,
        width: 40,
        height: 15,
        fontSize: 18,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'left',
        maxLength: 40,
        placeholder: 'Galaxy brain level'
      }
    ]
  },
  {
    id: 'trapgod-roast',
    name: 'TrapGod Roast Card',
    description: 'Custom TrapGPT roast card format',
    imageUrl: '/meme-templates/trapgod-roast-template.jpg',
    category: 'trap',
    textAreas: [
      {
        id: 'target-name',
        x: 50,
        y: 15,
        width: 80,
        height: 10,
        fontSize: 28,
        fontFamily: 'Orbitron, sans-serif',
        color: '#0cf5ff',
        textAlign: 'center',
        maxLength: 30,
        placeholder: 'Target Developer Name'
      },
      {
        id: 'roast-text',
        x: 50,
        y: 45,
        width: 85,
        height: 30,
        fontSize: 20,
        fontFamily: 'VT323, monospace',
        color: '#ffffff',
        textAlign: 'center',
        maxLength: 200,
        placeholder: 'Roast content goes here'
      },
      {
        id: 'damage-stat',
        x: 20,
        y: 80,
        width: 25,
        height: 8,
        fontSize: 16,
        fontFamily: 'Orbitron, sans-serif',
        color: '#ff6b6b',
        textAlign: 'center',
        maxLength: 10,
        placeholder: 'DAMAGE: 9999'
      },
      {
        id: 'rank',
        x: 80,
        y: 80,
        width: 25,
        height: 8,
        fontSize: 16,
        fontFamily: 'Orbitron, sans-serif',
        color: '#9b87f5',
        textAlign: 'center',
        maxLength: 20,
        placeholder: 'ATOMIC GREMLIN'
      }
    ]
  },
  {
    id: 'dev-struggle',
    name: 'Developer Struggle',
    description: 'Relatable developer pain points',
    imageUrl: '/meme-templates/dev-struggle-template.jpg',
    category: 'dev',
    textAreas: [
      {
        id: 'problem',
        x: 50,
        y: 20,
        width: 80,
        height: 25,
        fontSize: 22,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 80,
        placeholder: 'The coding problem'
      },
      {
        id: 'solution',
        x: 50,
        y: 70,
        width: 80,
        height: 25,
        fontSize: 22,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 80,
        placeholder: 'The "solution"'
      }
    ]
  },
  {
    id: 'gremlin-wisdom',
    name: 'Gremlin Wisdom',
    description: 'Lil LiL dispensing trap wisdom',
    imageUrl: '/meme-templates/gremlin-wisdom-template.jpg',
    category: 'trap',
    textAreas: [
      {
        id: 'wisdom-text',
        x: 50,
        y: 75,
        width: 90,
        height: 20,
        fontSize: 24,
        fontFamily: 'VT323, monospace',
        color: '#0cf5ff',
        textAlign: 'center',
        maxLength: 100,
        placeholder: 'Gremlin wisdom goes here'
      }
    ]
  },
  {
    id: 'code-review',
    name: 'Code Review Reaction',
    description: 'Reactions to code reviews',
    imageUrl: '/meme-templates/code-review-template.jpg',
    category: 'dev',
    textAreas: [
      {
        id: 'review-comment',
        x: 50,
        y: 15,
        width: 80,
        height: 15,
        fontSize: 20,
        fontFamily: 'Arial, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 60,
        placeholder: 'Code review comment'
      },
      {
        id: 'developer-reaction',
        x: 50,
        y: 80,
        width: 80,
        height: 15,
        fontSize: 20,
        fontFamily: 'Impact, sans-serif',
        color: '#000000',
        textAlign: 'center',
        maxLength: 60,
        placeholder: 'Developer reaction'
      }
    ]
  }
];

// Generate meme content based on roast and profile
export function generateMemeContent(
  templateId: string,
  roastText: string,
  targetName: string,
  damage: number,
  rank: string
): Record<string, string> {
  const template = MEME_TEMPLATES.find(t => t.id === templateId);
  if (!template) return {};

  const content: Record<string, string> = {};

  switch (templateId) {
    case 'trapgod-roast':
      content['target-name'] = targetName;
      content['roast-text'] = roastText;
      content['damage-stat'] = `DAMAGE: ${damage}`;
      content['rank'] = rank;
      break;

    case 'drake-pointing':
      content['top-text'] = `${targetName}'s current code`;
      content['bottom-text'] = roastText;
      break;

    case 'expanding-brain':
      const levels = roastText.split('.').slice(0, 4);
      content['level-1'] = levels[0] || `${targetName}'s basic code`;
      content['level-2'] = levels[1] || 'Thinking they\'re good';
      content['level-3'] = levels[2] || 'Actually getting roasted';
      content['level-4'] = levels[3] || 'JUUWRAAYYY DESTRUCTION';
      break;

    case 'dev-struggle':
      content['problem'] = `${targetName}'s coding skills`;
      content['solution'] = roastText;
      break;

    case 'gremlin-wisdom':
      content['wisdom-text'] = `"${roastText}" - Lil LiL da TrapGod`;
      break;

    case 'code-review':
      content['review-comment'] = `Reviewing ${targetName}'s code`;
      content['developer-reaction'] = roastText;
      break;

    default:
      // Generic fallback
      if (template.textAreas.length > 0) {
        content[template.textAreas[0].id] = roastText;
      }
  }

  return content;
}

// Get templates by category
export function getTemplatesByCategory(category: MemeTemplate['category']): MemeTemplate[] {
  return MEME_TEMPLATES.filter(template => template.category === category);
}

// Get random template
export function getRandomTemplate(): MemeTemplate {
  return MEME_TEMPLATES[Math.floor(Math.random() * MEME_TEMPLATES.length)];
}

// Get template by ID
export function getTemplateById(id: string): MemeTemplate | undefined {
  return MEME_TEMPLATES.find(template => template.id === id);
}
