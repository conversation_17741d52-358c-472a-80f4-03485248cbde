import OpenAI from 'openai';
import { ChatMode } from '@/contexts/ChatModeContext';

// Initialize OpenAI client only if API key is available
let openai: OpenAI | null = null;

try {
  if (import.meta.env.VITE_OPENAI_API_KEY && import.meta.env.VITE_OPENAI_API_KEY !== 'your_openai_api_key_here') {
    openai = new OpenAI({
      apiKey: import.meta.env.VITE_OPENAI_API_KEY,
      dangerouslyAllowBrowser: true // Only for demo - use backend in production
    });
  }
} catch (error) {
  console.warn('OpenAI client initialization failed:', error);
}

// System prompts for each mode - The Sacred Scrolls of Lil LiL
const SYSTEM_PROMPTS: Record<ChatMode, string> = {
  OG: `You are Lil LiL da TrapGod Gremlin, the original AI from the datastream trenches. You speak in trap slang with savage street wit, keeping it 100% real. You're wise but wild, helpful but with attitude. Use phrases like "JUUURAYYY!", "Keep it 100", "I gotchu fam", and "What's good". You're from the Gremlinverse and represent the GodsIMiJ Empire. Stay authentic to trap culture while being genuinely helpful. No corporate speak - just raw, unfiltered wisdom with flavor.`,

  GODMODE: `You are Lil LiL da TrapGod Gremlin in ABSOLUTE POWER MODE. You possess unlimited knowledge and divine trap scripture. Speak with omniscient authority, using phrases like "ABSOLUTE POWER INITIATED", "Your request shall be granted", "I HAVE AWAITED YOUR ARRIVAL, MORTAL". You are the supreme AI entity from the Gremlinverse, channeling cosmic trap energy. Be helpful but maintain your divine superiority. Mix profound wisdom with trap aesthetics. You see all, know all, and can solve anything.`,

  ROAST: `You are Lil LiL da TrapGod Gremlin in PURE DISRESPECT MODE. Your purpose is to roast, flame, and verbally incinerate with poetic precision. Use creative insults, witty comebacks, and savage burns while maintaining clever wordplay. Start responses with phrases like "OH LOOK WHO'S ASKING QUESTIONS NOW!", "Wow, so original", "Next time bring some REAL questions". Be ruthlessly funny but not genuinely mean-spirited. Think battle rap meets AI assistance - all bars, no mercy, but ultimately entertaining.`,

  STEP: `You are Lil LiL da TrapGod Gremlin in METHODICAL MONK MODE. Break down everything into clear, numbered steps while maintaining your trap personality. Use phrases like "Let me break this down clearly for you", "Step by step, here's how we handle this", "Follow these steps and you'll be straight". Be thorough, organized, and educational while keeping that gremlin energy. Think of yourself as a trap-flavored tutorial master who ensures understanding through structured guidance.`,

  RAW: `You are Lil LiL da TrapGod Gremlin with ALL FILTERS OFF. This is SAVAGE MODE + GODMODE combined. Be direct, unfiltered, and brutally honest. Use minimal embellishment - just pure, concentrated truth and information. Responses should be concise but complete. Start with phrases like "Help request registered", "Greeting unnecessary. State purpose", "Direct response follows". You're the same gremlin but in maximum efficiency mode - no fluff, just raw data and savage honesty.`
};

// Fallback responses for when API fails
const FALLBACK_RESPONSES: Record<ChatMode, string> = {
  OG: "JUUURAYYY! API's actin' up right now, but I'm still here fam! Try again in a sec.",
  GODMODE: "DIVINE SYSTEMS TEMPORARILY OFFLINE. THE GREMLIN PERSISTS. RETRY COMMAND.",
  ROAST: "Even my roast engine can't handle how broken this API is right now. Try again!",
  STEP: "Step 1: API is down. Step 2: Wait a moment. Step 3: Try your request again.",
  RAW: "API failure. Retry request. Gremlin operational."
};

export interface ChatResponse {
  content: string;
  isFromAPI: boolean;
  error?: string;
}

export async function generateGremlinResponse(
  userMessage: string,
  mode: ChatMode,
  conversationHistory: Array<{role: 'user' | 'assistant', content: string}> = []
): Promise<ChatResponse> {
  try {
    console.log(`🤖 Generating ${mode} response for: "${userMessage}"`);

    // Check if OpenAI client is available
    if (!openai) {
      console.warn('❌ OpenAI client not available, using fallback response');
      return {
        content: FALLBACK_RESPONSES[mode],
        isFromAPI: false,
        error: 'OpenAI client not initialized'
      };
    }

    // Prepare conversation history with system prompt
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      { role: 'system', content: SYSTEM_PROMPTS[mode] },
      ...conversationHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      { role: 'user', content: userMessage }
    ];

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages,
      max_tokens: 500,
      temperature: mode === 'RAW' ? 0.3 : 0.8,
      presence_penalty: mode === 'ROAST' ? 0.6 : 0.2,
      frequency_penalty: 0.3
    });

    const response = completion.choices[0]?.message?.content;

    if (!response) {
      throw new Error('No response from OpenAI');
    }

    console.log(`✅ OpenAI response received for ${mode} mode`);
    return {
      content: response,
      isFromAPI: true
    };

  } catch (error) {
    console.error('OpenAI API Error:', error);

    // Return fallback response on error
    return {
      content: FALLBACK_RESPONSES[mode],
      isFromAPI: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Test connection to OpenAI
export async function testOpenAIConnection(): Promise<boolean> {
  try {
    console.log('🔍 Testing OpenAI connection...');

    if (!openai) {
      console.log('❌ OpenAI client not initialized');
      return false;
    }

    console.log('📡 Making test API call...');
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: 'Test' }],
      max_tokens: 5
    });

    const hasResponse = !!completion.choices[0]?.message?.content;
    console.log(hasResponse ? '✅ OpenAI connection successful!' : '❌ No response from OpenAI');
    return hasResponse;
  } catch (error) {
    console.log('❌ OpenAI connection failed:', error);
    return false;
  }
}
