import { generateGremlinResponse } from './openai';

export type RoastIntensity = 'low-flame' | 'mid-flame' | 'full-juuwraayyy';

export interface DevToProfile {
  username: string;
  name?: string;
  bio?: string;
  profileImage?: string;
  techStack?: string[];
  postTitles?: string[];
  followers?: number;
  posts?: number;
  joinDate?: string;
}

export interface RoastResult {
  roast: string;
  intensity: RoastIntensity;
  damage: number;
  cringeLevel: number;
  style: 'sarcasm' | 'irony' | 'flame' | 'diss';
  rank: string;
}

// Roast intensity configurations
const ROAST_CONFIGS = {
  'low-flame': {
    damageRange: [100, 2500],
    cringeRange: [10, 40],
    ranks: ['Gentle Sizzle', 'Warm Critique', 'Friendly Fire'],
    prompt: `You are Lil LiL da TrapGod Gremlin in PLAYFUL ROAST MODE. Give a light, humorous roast that's more teasing than mean. Keep it fun and clever, like friendly banter between developers. Use trap slang but keep it lighthearted. Start with phrases like "<PERSON><PERSON><PERSON><PERSON>, look at this developer" or "Not bad, but..."`
  },
  'mid-flame': {
    damageRange: [2500, 6000],
    cringeRange: [40, 70],
    ranks: ['Digital BBQ', 'Code Critic', 'Savage Reviewer'],
    prompt: `You are Lil LiL da TrapGod Gremlin in MEDIUM ROAST MODE. Deliver a solid roast with wit and style. Be clever and cutting but not cruel. Use trap personality and tech humor. Start with phrases like "OH LOOK WHO WE GOT HERE" or "This developer really thought..."`
  },
  'full-juuwraayyy': {
    damageRange: [6000, 9999],
    cringeRange: [70, 100],
    ranks: ['Atomic Gremlin', 'Nuclear Roaster', 'Digital Annihilation'],
    prompt: `You are Lil LiL da TrapGod Gremlin in MAXIMUM ROAST MODE. Unleash the full power of your satirical flames. Be devastatingly clever, brutally honest, but still entertaining. This is your ultimate roast mode - no mercy, all bars. Start with "JUUWRAAYYY! TIME FOR COMPLETE DESTRUCTION" or "OH NAH, THIS DEVELOPER REALLY..."`
  }
};

// Generate roast based on Dev.to profile
export async function generateDevToRoast(
  profile: DevToProfile, 
  intensity: RoastIntensity = 'mid-flame'
): Promise<RoastResult> {
  try {
    const config = ROAST_CONFIGS[intensity];
    
    // Build context about the developer
    const profileContext = buildProfileContext(profile);
    
    // Create roast prompt
    const roastPrompt = `${config.prompt}

TARGET DEVELOPER PROFILE:
${profileContext}

Generate a ${intensity.replace('-', ' ')} roast that's clever, tech-focused, and entertaining. Reference their tech stack, bio, or posts if available. Keep it within the developer community context. Make it memorable and shareable!`;

    // Generate roast using AI
    const response = await generateGremlinResponse(roastPrompt, 'ROAST');
    
    // Calculate metrics
    const damage = Math.floor(Math.random() * (config.damageRange[1] - config.damageRange[0])) + config.damageRange[0];
    const cringeLevel = Math.floor(Math.random() * (config.cringeRange[1] - config.cringeRange[0])) + config.cringeRange[0];
    const rank = config.ranks[Math.floor(Math.random() * config.ranks.length)];
    const style = determineRoastStyle(response.content);

    return {
      roast: response.content,
      intensity,
      damage,
      cringeLevel,
      style,
      rank
    };

  } catch (error) {
    console.error('Roast generation failed:', error);
    
    // Fallback roast
    return generateFallbackRoast(profile, intensity);
  }
}

// Build context string from profile data
function buildProfileContext(profile: DevToProfile): string {
  const context = [];
  
  context.push(`Username: ${profile.username}`);
  
  if (profile.name) context.push(`Name: ${profile.name}`);
  if (profile.bio) context.push(`Bio: ${profile.bio}`);
  if (profile.techStack?.length) context.push(`Tech Stack: ${profile.techStack.join(', ')}`);
  if (profile.postTitles?.length) context.push(`Recent Posts: ${profile.postTitles.slice(0, 3).join(', ')}`);
  if (profile.followers) context.push(`Followers: ${profile.followers}`);
  if (profile.posts) context.push(`Posts: ${profile.posts}`);
  if (profile.joinDate) context.push(`Joined: ${profile.joinDate}`);
  
  return context.join('\n');
}

// Determine roast style based on content
function determineRoastStyle(roast: string): 'sarcasm' | 'irony' | 'flame' | 'diss' {
  const lowerRoast = roast.toLowerCase();
  
  if (lowerRoast.includes('really') || lowerRoast.includes('sure') || lowerRoast.includes('totally')) {
    return 'sarcasm';
  }
  if (lowerRoast.includes('ironic') || lowerRoast.includes('funny how') || lowerRoast.includes('interesting')) {
    return 'irony';
  }
  if (lowerRoast.includes('fire') || lowerRoast.includes('burn') || lowerRoast.includes('flame')) {
    return 'flame';
  }
  return 'diss';
}

// Fallback roast generator
function generateFallbackRoast(profile: DevToProfile, intensity: RoastIntensity): RoastResult {
  const config = ROAST_CONFIGS[intensity];
  
  const fallbackRoasts = {
    'low-flame': [
      `Ayyy ${profile.username}, your code probably works... most of the time! 😏`,
      `Not bad ${profile.username}, but I've seen more exciting commit messages! 🔥`,
      `${profile.username} out here coding like it's still 2010! Keep up fam! 💻`
    ],
    'mid-flame': [
      `OH LOOK WHO WE GOT HERE! ${profile.username} really thought that bio was gonna impress somebody! 🔥`,
      `${profile.username}'s tech stack looking like a grocery list - just throwing everything in there! 💀`,
      `This developer ${profile.username} really said "${profile.bio}" and thought we wouldn't notice! JUUWRAAYYY! 🎭`
    ],
    'full-juuwraayyy': [
      `JUUWRAAYYY! TIME FOR COMPLETE DESTRUCTION! ${profile.username} really out here with ${profile.followers || 0} followers thinking they're the next tech influencer! 💀🔥`,
      `OH NAH, ${profile.username} REALLY thought that profile was gonna get them hired! The audacity! 🎭💀`,
      `ATOMIC GREMLIN MODE ACTIVATED! ${profile.username}'s code probably has more bugs than a rainforest! JUUWRAAYYY! 🧠🔥`
    ]
  };
  
  const roasts = fallbackRoasts[intensity];
  const roast = roasts[Math.floor(Math.random() * roasts.length)];
  
  const damage = Math.floor(Math.random() * (config.damageRange[1] - config.damageRange[0])) + config.damageRange[0];
  const cringeLevel = Math.floor(Math.random() * (config.cringeRange[1] - config.cringeRange[0])) + config.cringeRange[0];
  const rank = config.ranks[Math.floor(Math.random() * config.ranks.length)];
  
  return {
    roast,
    intensity,
    damage,
    cringeLevel,
    style: 'diss',
    rank
  };
}

// Extract Dev.to profile from URL or username
export async function fetchDevToProfile(usernameOrUrl: string): Promise<DevToProfile | null> {
  try {
    // Extract username from URL if provided
    const username = usernameOrUrl.includes('dev.to') 
      ? usernameOrUrl.split('/').pop()?.replace('@', '') || usernameOrUrl
      : usernameOrUrl.replace('@', '');

    // In a real implementation, this would call the Dev.to API
    // For now, return a mock profile structure
    console.log(`🔍 Fetching Dev.to profile for: ${username}`);
    
    // Mock profile data - in production this would be real API calls
    return {
      username,
      name: `Developer ${username}`,
      bio: `Passionate developer building the future with code`,
      techStack: ['JavaScript', 'React', 'Node.js'],
      postTitles: ['How I Built My First App', '10 Tips for Better Code'],
      followers: Math.floor(Math.random() * 1000),
      posts: Math.floor(Math.random() * 50),
      joinDate: '2023'
    };
    
  } catch (error) {
    console.error('Failed to fetch Dev.to profile:', error);
    return null;
  }
}
