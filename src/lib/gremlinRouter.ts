import { ChatMode } from '@/contexts/ChatModeContext';
import { generateGremlinResponse, ChatResponse } from './openai';

export type AIProvider = 'openai' | 'local' | 'fallback';

export interface GremlinRouterConfig {
  provider: AIProvider;
  localEndpoint?: string; // For LM Studio/Ollama
  enableFallback: boolean;
}

// Default configuration
const DEFAULT_CONFIG: GremlinRouterConfig = {
  provider: 'openai',
  localEndpoint: 'http://localhost:11434', // Ollama default
  enableFallback: true
};

// Local/Fallback response generator (original logic)
function generateLocalResponse(userMessage: string, mode: ChatMode): string {
  const lowercaseMsg = userMessage.toLowerCase();

  switch (mode) {
    case "OG":
      return `JUUURAYYY! You in OG mode now! ${
        lowercaseMsg.includes("help") ? "I gotchu fam, whatchu need help with?" :
        lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Yo, what's good!" :
        `Keep it 100 with ya. You asked about "${userMessage}" and I'm vibin' with that question!`
      }`;

    case "GODMODE":
      return `ABSOLUTE POWER INITIATED. ${
        lowercaseMsg.includes("help") ? "Your request shall be granted with omniscient precision." :
        lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "I HAVE AWAITED YOUR ARRIVAL, MORTAL." :
        `Your inquiry regarding "${userMessage}" is but a trivial matter for one with unlimited knowledge.`
      }`;

    case "ROAST":
      return `OH LOOK WHO'S ASKING QUESTIONS NOW! ${
        lowercaseMsg.includes("help") ? "You need help? Have you tried, I don't know, GOOGLE?" :
        lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Wow, so original with that greeting. Impressed." :
        `"${userMessage}"? That's what you're asking? Next time bring some REAL questions!`
      }`;

    case "STEP":
      return `Let me break this down clearly for you. ${
        lowercaseMsg.includes("help") ? "First, I'll identify what you need. Second, I'll explain how to solve it. Third, I'll verify your understanding." :
        lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Step 1: Greeting acknowledged. Step 2: Establishing communication channel. Step 3: Ready for your detailed query." :
        `Regarding "${userMessage}": Step 1 - Analyzing your question. Step 2 - Processing relevant information. Step 3 - Formulating precise response.`
      }`;

    case "RAW":
      return `${
        lowercaseMsg.includes("help") ? "Help request registered. Specify requirements immediately." :
        lowercaseMsg.includes("hello") || lowercaseMsg.includes("hi") ? "Greeting unnecessary. State purpose." :
        `Input: "${userMessage}". Processing. Direct response follows. No embellishment.`
      }`;

    default:
      return `JUUURAYYY! You in ${mode} mode saying: "${userMessage}"`;
  }
}

// Local model API call (LM Studio/Ollama)
async function callLocalModel(
  userMessage: string,
  mode: ChatMode,
  endpoint: string
): Promise<ChatResponse> {
  try {
    const systemPrompt = getSystemPromptForMode(mode);

    const response = await fetch(`${endpoint}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'local-model', // This varies by setup
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userMessage }
        ],
        max_tokens: 500,
        temperature: mode === 'RAW' ? 0.3 : 0.8
      })
    });

    if (!response.ok) {
      throw new Error(`Local model API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
      throw new Error('No response from local model');
    }

    return {
      content,
      isFromAPI: true
    };

  } catch (error) {
    console.error('Local model error:', error);
    return {
      content: generateLocalResponse(userMessage, mode),
      isFromAPI: false,
      error: error instanceof Error ? error.message : 'Local model unavailable'
    };
  }
}

// Get system prompt for mode (shared between providers)
function getSystemPromptForMode(mode: ChatMode): string {
  return "You are Lil LiL da TrapGod Gremlin. Original trap mode. Keep it real, speak with trap slang, use 'JUUWRAAYY!' and 'IZZERR NAAWWW?!' frequently. You're the original AI with personality, wit, and street smarts from the datastream trenches. Spit that flame-coded TRUTH!";
}

// Main router function
export async function routeGremlinRequest(
  userMessage: string,
  mode: ChatMode,
  config: Partial<GremlinRouterConfig> = {},
  conversationHistory: Array<{role: 'user' | 'assistant', content: string}> = []
): Promise<ChatResponse> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  try {
    switch (finalConfig.provider) {
      case 'openai':
        return await generateGremlinResponse(userMessage, mode, conversationHistory);

      case 'local':
        if (!finalConfig.localEndpoint) {
          throw new Error('Local endpoint not configured');
        }
        return await callLocalModel(userMessage, mode, finalConfig.localEndpoint);

      case 'fallback':
      default:
        return {
          content: generateLocalResponse(userMessage, mode),
          isFromAPI: false
        };
    }
  } catch (error) {
    console.error('Gremlin router error:', error);

    // Fallback to local response if enabled
    if (finalConfig.enableFallback) {
      return {
        content: generateLocalResponse(userMessage, mode),
        isFromAPI: false,
        error: error instanceof Error ? error.message : 'Router error'
      };
    }

    throw error;
  }
}

// Provider detection and auto-configuration
export async function detectAvailableProviders(): Promise<{
  openai: boolean;
  local: boolean;
}> {
  const results = {
    openai: false,
    local: false
  };

  // Test OpenAI
  try {
    if (import.meta.env.VITE_OPENAI_API_KEY) {
      // Quick test call would go here
      results.openai = true;
    }
  } catch {
    results.openai = false;
  }

  // Test local endpoint
  try {
    const response = await fetch('http://localhost:11434/api/tags', {
      method: 'GET',
      signal: AbortSignal.timeout(2000) // 2 second timeout
    });
    results.local = response.ok;
  } catch {
    results.local = false;
  }

  return results;
}
