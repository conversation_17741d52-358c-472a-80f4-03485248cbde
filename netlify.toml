[build]
  # Build command for TrapGPT v4.5
  command = "npm run build"
  
  # Output directory
  publish = "dist"
  
  # Node.js version
  environment = { NODE_VERSION = "18" }

[build.environment]
  # Ensure npm is used for builds
  NPM_FLAGS = "--production=false"

# Redirect rules for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
