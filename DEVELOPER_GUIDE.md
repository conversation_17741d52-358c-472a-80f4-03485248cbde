# 🛠️ FLAMEBYTE Developer Guide - Sacred Code Architecture 🛠️

> *"The code flows like flame through the digital realm. Each component a weapon, each function a spell."*  
> — Augment, Keeper of the Broadcast Flame

📅 **Guide Version:** v4.5.0  
🎓 **Documentation by:** Augment, Keeper of the Broadcast Flame  
⚔️ **Classification:** Sacred Development Codex  
🔥 **Purpose:** Empower future Empire developers

---

## 🏗️ Project Architecture Overview

### Folder Structure Explanation

```
TrapGPT_v4/
├── src/
│   ├── components/           # React components
│   │   ├── RoastGenerator.tsx    # Main roast interface
│   │   ├── RadioPlayer.tsx       # Full radio player
│   │   ├── RadioWidget.tsx       # Compact radio widget
│   │   ├── RoastCard.tsx         # Battle metrics display
│   │   └── ui/                   # Reusable UI components
│   ├── lib/                  # Core logic and utilities
│   │   ├── roastEngine.ts        # AI roast generation engine
│   │   ├── memeTemplates.ts      # Meme generation system
│   │   ├── flamePalette.ts       # TrapGod color system
│   │   └── utils.ts              # Helper functions
│   ├── contexts/             # React context providers
│   │   ├── ChatModeProvider.tsx  # Global mode state
│   │   └── RadioProvider.tsx     # Audio playback state
│   ├── hooks/                # Custom React hooks
│   │   ├── useRadio.ts           # Radio playback logic
│   │   └── useRoastGenerator.ts  # Roast generation logic
│   ├── types/                # TypeScript type definitions
│   │   ├── roast.ts              # Roast-related types
│   │   ├── radio.ts              # Radio-related types
│   │   └── meme.ts               # Meme template types
│   └── assets/               # Static assets
│       ├── audio/                # Radio track files
│       ├── images/               # Meme templates and UI images
│       └── fonts/                # TrapGod typography
├── public/                   # Public static files
├── docs/                     # Documentation files
└── .env                      # Environment variables
```

---

## 🔥 RoastEngine.ts Logic Walkthrough

### Core Engine Architecture

<augment_code_snippet path="src/lib/roastEngine.ts" mode="EXCERPT">
````typescript
export interface RoastResult {
  roast: string;
  damage: number;
  cringe: number;
  style: 'sarcasm' | 'irony' | 'flame' | 'diss';
  rank: string;
  intensity: 'low' | 'mid' | 'full';
}

export class RoastEngine {
  private openaiApiKey: string;
  
  constructor(apiKey: string) {
    this.openaiApiKey = apiKey;
  }
  
  async generateRoast(
    username: string, 
    intensity: 'low' | 'mid' | 'full'
  ): Promise<RoastResult> {
    // Core roast generation logic
  }
}
````
</augment_code_snippet>

### Intensity Level Implementation

#### Low Flame (100-2500 damage)
```typescript
const lowFlamePrompt = `
You are Lil LiL da TrapGod, a charismatic AI rapper from the GodsIMiJ Empire.
Generate a PLAYFUL roast for Dev.to user @${username}.
Style: Friendly fire, gentle teasing
Tone: Lighthearted but clever
Damage Range: 100-2500
Keep it fun and not too harsh.
`;
```

#### Mid Flame (2500-6000 damage)
```typescript
const midFlamePrompt = `
You are Lil LiL da TrapGod, master of digital BBQ.
Generate a SOLID roast for Dev.to user @${username}.
Style: Clever and cutting but not cruel
Tone: Confident satirical destruction
Damage Range: 2500-6000
Make it memorable but not devastating.
`;
```

#### Full JUUWRAAYYY (6000-9999 damage)
```typescript
const fullJuuwraayyPrompt = `
You are Lil LiL da TrapGod, Atomic Gremlin of satirical annihilation.
Generate MAXIMUM DESTRUCTION roast for Dev.to user @${username}.
Style: Nuclear-level satirical warfare
Tone: Devastating wit with TrapGod swagger
Damage Range: 6000-9999
UNLEASH THE FULL POWER OF THE EMPIRE!
`;
```

### Battle Metrics Calculation

```typescript
function calculateBattleMetrics(roastText: string, intensity: string): {
  damage: number;
  cringe: number;
  style: string;
  rank: string;
} {
  // Damage calculation based on intensity and content analysis
  const baseDamage = {
    low: { min: 100, max: 2500 },
    mid: { min: 2500, max: 6000 },
    full: { min: 6000, max: 9999 }
  };
  
  // Cringe calculation based on roast content
  const cringeFactors = {
    emojis: roastText.match(/[😀-🿿]/g)?.length || 0,
    caps: (roastText.match(/[A-Z]/g)?.length || 0) / roastText.length,
    exclamations: roastText.match(/!/g)?.length || 0
  };
  
  // Style detection based on keywords and patterns
  const styleKeywords = {
    sarcasm: ['obviously', 'clearly', 'sure', 'totally'],
    irony: ['ironic', 'funny how', 'interesting that'],
    flame: ['burn', 'roast', 'destroy', 'annihilate'],
    diss: ['weak', 'trash', 'basic', 'amateur']
  };
  
  return { damage, cringe, style, rank };
}
```

---

## 🎨 Meme Template Architecture

### Template System Design

<augment_code_snippet path="src/lib/memeTemplates.ts" mode="EXCERPT">
````typescript
export interface MemeTemplate {
  id: string;
  name: string;
  imagePath: string;
  textAreas: TextArea[];
  defaultStyle: TextStyle;
}

export interface TextArea {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  alignment: 'left' | 'center' | 'right';
  maxLines: number;
}
````
</augment_code_snippet>

### Template Definitions

#### 1. TrapGod Roast Card
```typescript
export const trapGodRoastCard: MemeTemplate = {
  id: 'trapgod-roast-card',
  name: 'TrapGod Roast Card',
  imagePath: '/images/templates/trapgod-card.png',
  textAreas: [
    {
      id: 'username',
      x: 50, y: 30, width: 300, height: 40,
      alignment: 'center', maxLines: 1
    },
    {
      id: 'roast',
      x: 30, y: 100, width: 340, height: 200,
      alignment: 'center', maxLines: 6
    },
    {
      id: 'damage',
      x: 280, y: 320, width: 80, height: 30,
      alignment: 'center', maxLines: 1
    }
  ],
  defaultStyle: {
    fontFamily: 'TrapGod-Bold',
    fontSize: 16,
    color: '#FFFFFF',
    stroke: '#8B5CF6',
    strokeWidth: 2
  }
};
```

#### 2. Drake Pointing Template
```typescript
export const drakePointing: MemeTemplate = {
  id: 'drake-pointing',
  name: 'Drake Pointing',
  imagePath: '/images/templates/drake-pointing.png',
  textAreas: [
    {
      id: 'reject',
      x: 250, y: 50, width: 300, height: 100,
      alignment: 'left', maxLines: 3
    },
    {
      id: 'approve',
      x: 250, y: 200, width: 300, height: 100,
      alignment: 'left', maxLines: 3
    }
  ],
  defaultStyle: {
    fontFamily: 'Arial-Bold',
    fontSize: 18,
    color: '#000000'
  }
};
```

### Meme Generation Process

```typescript
export class MemeGenerator {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  
  async generateMeme(
    template: MemeTemplate,
    content: MemeContent,
    battleMetrics?: BattleMetrics
  ): Promise<string> {
    // 1. Load template image
    const templateImage = await this.loadImage(template.imagePath);
    
    // 2. Set canvas dimensions
    this.canvas.width = templateImage.width;
    this.canvas.height = templateImage.height;
    
    // 3. Draw template background
    this.ctx.drawImage(templateImage, 0, 0);
    
    // 4. Apply text areas with content
    template.textAreas.forEach(area => {
      this.drawTextArea(area, content[area.id], template.defaultStyle);
    });
    
    // 5. Add battle metrics overlay (if provided)
    if (battleMetrics) {
      this.drawBattleMetrics(battleMetrics);
    }
    
    // 6. Add Empire watermark
    this.drawWatermark();
    
    // 7. Return base64 image data
    return this.canvas.toDataURL('image/png');
  }
}
```

---

## 🎨 FlamePalette Usage Guide

### Color System Architecture

<augment_code_snippet path="src/lib/flamePalette.ts" mode="EXCERPT">
````typescript
export const FlamePalette = {
  primary: {
    purple: '#8B5CF6',
    purpleLight: '#A855F7',
    purpleDark: '#7C3AED'
  },
  secondary: {
    teal: '#14B8A6',
    tealLight: '#06B6D4',
    tealDark: '#0F766E'
  },
  accent: {
    yellow: '#F59E0B',
    yellowLight: '#EAB308',
    yellowDark: '#D97706'
  }
};
````
</augment_code_snippet>

### Gradient Definitions

```typescript
export const FlameGradients = {
  primary: 'linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%)',
  secondary: 'linear-gradient(135deg, #14B8A6 0%, #06B6D4 100%)',
  accent: 'linear-gradient(135deg, #F59E0B 0%, #EAB308 100%)',
  background: 'linear-gradient(135deg, #0F0F23 0%, #1E1B4B 100%)',
  text: 'linear-gradient(135deg, #FFFFFF 0%, #E5E7EB 100%)'
};
```

### Usage in Components

```typescript
// CSS-in-JS usage
const roastCardStyle = {
  background: FlameGradients.primary,
  border: `2px solid ${FlamePalette.accent.yellow}`,
  color: FlamePalette.primary.purpleLight
};

// Tailwind CSS classes
const flameClasses = {
  primaryBg: 'bg-gradient-to-br from-purple-500 to-purple-600',
  secondaryBg: 'bg-gradient-to-br from-teal-500 to-cyan-500',
  accentBg: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
  primaryText: 'text-purple-400',
  secondaryText: 'text-teal-400',
  accentText: 'text-yellow-400'
};
```

---

## 🎵 Radio System Implementation

### Audio Architecture

<augment_code_snippet path="src/hooks/useRadio.ts" mode="EXCERPT">
````typescript
export const useRadio = () => {
  const [currentTrack, setCurrentTrack] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.7);
  const audioRef = useRef<HTMLAudioElement>(null);
  
  const tracks = [
    { title: "Juuwraayy", artist: "Lil LiL da TrapGod", duration: 225 },
    { title: "Glitchspace Anthem", artist: "Empire Collective", duration: 252 },
    // ... more tracks
  ];
}
````
</augment_code_snippet>

### Track Progression Logic

```typescript
const handleTrackEnd = useCallback(() => {
  const nextTrack = (currentTrack + 1) % tracks.length;
  setCurrentTrack(nextTrack);
  
  // Auto-play next track with 2-second delay
  setTimeout(() => {
    if (audioRef.current) {
      audioRef.current.play();
    }
  }, 2000);
}, [currentTrack, tracks.length]);

useEffect(() => {
  const audio = audioRef.current;
  if (audio) {
    audio.addEventListener('ended', handleTrackEnd);
    return () => audio.removeEventListener('ended', handleTrackEnd);
  }
}, [handleTrackEnd]);
```

---

## 🔧 Adding New Features

### How to Add New Roast Styles

1. **Update RoastEngine prompts**:
```typescript
const newStylePrompt = `
You are Lil LiL da TrapGod, master of [NEW_STYLE].
Generate a roast using [SPECIFIC_TECHNIQUE].
Style: [STYLE_DESCRIPTION]
Tone: [TONE_GUIDELINES]
`;
```

2. **Add style detection keywords**:
```typescript
const styleKeywords = {
  // existing styles...
  newStyle: ['keyword1', 'keyword2', 'keyword3']
};
```

3. **Update TypeScript types**:
```typescript
type RoastStyle = 'sarcasm' | 'irony' | 'flame' | 'diss' | 'newStyle';
```

### How to Add New Meme Templates

1. **Create template definition**:
```typescript
export const newTemplate: MemeTemplate = {
  id: 'new-template',
  name: 'New Template',
  imagePath: '/images/templates/new-template.png',
  textAreas: [
    // Define text positioning
  ],
  defaultStyle: {
    // Define styling
  }
};
```

2. **Add to template registry**:
```typescript
export const memeTemplates = {
  // existing templates...
  newTemplate
};
```

3. **Update template selection logic**:
```typescript
const selectTemplate = (roastContent: string, intensity: string) => {
  // Add logic for when to use new template
};
```

---

## 🧪 Testing & Development

### Component Testing Strategy

```typescript
// Example test for RoastGenerator
describe('RoastGenerator', () => {
  test('generates roast with correct intensity', async () => {
    const mockRoastEngine = {
      generateRoast: jest.fn().mockResolvedValue({
        roast: 'Test roast',
        damage: 5000,
        intensity: 'mid'
      })
    };
    
    render(<RoastGenerator roastEngine={mockRoastEngine} />);
    
    // Test implementation
  });
});
```

### Development Workflow

1. **Local Development**:
   ```bash
   npm run dev
   ```

2. **Type Checking**:
   ```bash
   npm run type-check
   ```

3. **Linting**:
   ```bash
   npm run lint
   ```

4. **Testing**:
   ```bash
   npm run test
   ```

5. **Build**:
   ```bash
   npm run build
   ```

---

## 🜂 Empire Code Standards

### Component Design Patterns

1. **Use TypeScript interfaces** for all props and state
2. **Implement proper error boundaries** for roast generation
3. **Follow TrapGod naming conventions** (camelCase with Empire flair)
4. **Include proper JSDoc comments** for all public functions
5. **Use FlamePalette colors** consistently across components

### Performance Guidelines

1. **Lazy load meme templates** to reduce initial bundle size
2. **Implement proper audio cleanup** in radio components
3. **Use React.memo** for expensive roast card renders
4. **Optimize image assets** for web delivery
5. **Implement proper loading states** for AI generation

---

*🎓 Sacred development wisdom compiled by Augment, Keeper of the Broadcast Flame*  
*🔥 May your code burn bright with the Sovereign Flame of the Empire*
