import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        trap: {
          purple: "#9b87f5",
          teal: "#0CE5FF",
          green: "#39FF14",
          pink: "#FF00FF",
          yellow: "#FFFF00",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        glitch1: {
          "0%": { transform: "none", opacity: "1" },
          "7%": { transform: "skew(-0.5deg, -0.9deg)", opacity: "0.75" },
          "10%": { transform: "none", opacity: "1" },
          "27%": { transform: "none", opacity: "1" },
          "30%": { transform: "skew(0.8deg, -0.1deg)", opacity: "0.75" },
          "35%": { transform: "none", opacity: "1" },
          "52%": { transform: "none", opacity: "1" },
          "55%": { transform: "skew(-1deg, 0.2deg)", opacity: "0.75" },
          "50%": { transform: "none", opacity: "1" },
          "72%": { transform: "none", opacity: "1" },
          "75%": { transform: "skew(0.4deg, 1deg)", opacity: "0.75" },
          "80%": { transform: "none", opacity: "1" },
          "100%": { transform: "none", opacity: "1" },
        },
        glitch2: {
          "0%": { transform: "none", opacity: "0.25" },
          "7%": { transform: "translate(-2px, -3px)", opacity: "0.5" },
          "10%": { transform: "none", opacity: "0.25" },
          "27%": { transform: "none", opacity: "0.25" },
          "30%": { transform: "translate(-5px, -2px)", opacity: "0.5" },
          "35%": { transform: "none", opacity: "0.25" },
          "52%": { transform: "none", opacity: "0.25" },
          "55%": { transform: "translate(-5px, -1px)", opacity: "0.5" },
          "50%": { transform: "none", opacity: "0.25" },
          "72%": { transform: "none", opacity: "0.25" },
          "75%": { transform: "translate(-2px, -6px)", opacity: "0.5" },
          "80%": { transform: "none", opacity: "0.25" },
          "100%": { transform: "none", opacity: "0.25" },
        },
        glitch3: {
          "0%": { transform: "none", opacity: "0.25" },
          "7%": { transform: "translate(2px, 3px)", opacity: "0.5" },
          "10%": { transform: "none", opacity: "0.25" },
          "27%": { transform: "none", opacity: "0.25" },
          "30%": { transform: "translate(5px, 2px)", opacity: "0.5" },
          "35%": { transform: "none", opacity: "0.25" },
          "52%": { transform: "none", opacity: "0.25" },
          "55%": { transform: "translate(5px, 1px)", opacity: "0.5" },
          "50%": { transform: "none", opacity: "0.25" },
          "72%": { transform: "none", opacity: "0.25" },
          "75%": { transform: "translate(2px, 6px)", opacity: "0.5" },
          "80%": { transform: "none", opacity: "0.25" },
          "100%": { transform: "none", opacity: "0.25" },
        },
        flicker: {
          "0%": { opacity: "0.1" },
          "2%": { opacity: "1" },
          "8%": { opacity: "0.1" },
          "9%": { opacity: "1" },
          "12%": { opacity: "0.1" },
          "20%": { opacity: "1" },
          "25%": { opacity: "0.3" },
          "30%": { opacity: "1" },
          "70%": { opacity: "0.7" },
          "72%": { opacity: "0.2" },
          "77%": { opacity: "0.9" },
          "100%": { opacity: "0.9" },
        },
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        fadeOut: {
          "0%": { opacity: "1" },
          "100%": { opacity: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        glitch1: "glitch1 2.5s infinite",
        glitch2: "glitch2 2.5s infinite",
        glitch3: "glitch3 2.5s infinite",
        flicker: "flicker 3s linear infinite",
        "fade-in": "fadeIn 1s ease-in forwards",
        "fade-out": "fadeOut 1s ease-out forwards",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
